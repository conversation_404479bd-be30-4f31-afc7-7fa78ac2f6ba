import { setupServer } from 'msw/node'
import { rest } from 'msw'
import { mockUser } from '../utils'

// Mock API responses
export const handlers = [
  // Auth endpoints
  rest.post('/api/auth/login', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          user: mockUser,
          token: 'mock-jwt-token',
        },
      })
    )
  }),

  rest.post('/api/auth/register', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: {
          user: mockUser,
          token: 'mock-jwt-token',
        },
      })
    )
  }),

  rest.get('/api/auth/me', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: { user: mockUser },
      })
    )
  }),

  rest.post('/api/auth/logout', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        message: 'Logged out successfully',
      })
    )
  }),

  // Posts endpoints
  rest.get('/api/posts', (req, res, ctx) => {
    const mockPosts = [
      {
        id: '1',
        content: 'Test post 1',
        author: mockUser,
        likesCount: 5,
        commentsCount: 2,
        sharesCount: 1,
        isLiked: false,
        isSaved: false,
        privacy: 'public',
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      },
      {
        id: '2',
        content: 'Test post 2',
        author: mockUser,
        likesCount: 10,
        commentsCount: 5,
        sharesCount: 2,
        isLiked: true,
        isSaved: false,
        privacy: 'public',
        createdAt: '2023-01-02T00:00:00Z',
        updatedAt: '2023-01-02T00:00:00Z',
      },
    ]

    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          posts: mockPosts,
          pagination: {
            page: 1,
            limit: 10,
            total: 2,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
        },
      })
    )
  }),

  rest.get('/api/posts/:id', (req, res, ctx) => {
    const { id } = req.params
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          post: {
            id,
            content: `Test post ${id}`,
            author: mockUser,
            likesCount: 5,
            commentsCount: 2,
            sharesCount: 1,
            isLiked: false,
            isSaved: false,
            privacy: 'public',
            createdAt: '2023-01-01T00:00:00Z',
            updatedAt: '2023-01-01T00:00:00Z',
          },
        },
      })
    )
  }),

  rest.post('/api/posts', (req, res, ctx) => {
    return res(
      ctx.status(201),
      ctx.json({
        success: true,
        data: {
          post: {
            id: 'new-post-id',
            content: 'New test post',
            author: mockUser,
            likesCount: 0,
            commentsCount: 0,
            sharesCount: 0,
            isLiked: false,
            isSaved: false,
            privacy: 'public',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
        },
      })
    )
  }),

  rest.post('/api/likes', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        message: 'Post liked successfully',
      })
    )
  }),

  rest.delete('/api/likes', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        message: 'Post unliked successfully',
      })
    )
  }),

  // Users endpoints
  rest.get('/api/users/:id', (req, res, ctx) => {
    const { id } = req.params
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          user: {
            ...mockUser,
            id,
          },
        },
      })
    )
  }),

  rest.get('/api/users/suggested', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          users: [
            { ...mockUser, id: '2', name: 'Suggested User 1' },
            { ...mockUser, id: '3', name: 'Suggested User 2' },
          ],
        },
      })
    )
  }),

  // Notifications endpoints
  rest.get('/api/notifications', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        data: {
          notifications: [
            {
              id: '1',
              type: 'like',
              message: 'Someone liked your post',
              read: false,
              userId: mockUser.id,
              createdAt: '2023-01-01T00:00:00Z',
            },
          ],
        },
      })
    )
  }),

  // Error handlers
  rest.get('/api/error', (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({
        success: false,
        error: 'Internal server error',
      })
    )
  }),
]

export const server = setupServer(...handlers)
