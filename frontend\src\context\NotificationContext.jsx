import React, { createContext, useContext, useState, useEffect } from 'react'
import { useNotifications } from '../hooks'
import { NotificationToast } from '../components/notifications'

const NotificationContext = createContext()

export const useNotificationContext = () => {
  const context = useContext(NotificationContext)
  if (!context) {
    throw new Error('useNotificationContext must be used within a NotificationProvider')
  }
  return context
}

export const NotificationProvider = ({ children }) => {
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    refetch,
    getNotificationsByType,
    getUnreadNotifications,
    toastNotification,
    showToast,
    closeToast
  } = useNotifications()

  const [toastQueue, setToastQueue] = useState([])
  const [currentToast, setCurrentToast] = useState(null)

  // Handle toast queue
  useEffect(() => {
    if (toastNotification && showToast) {
      setToastQueue(prev => [...prev, toastNotification])
    }
  }, [toastNotification, showToast])

  // Process toast queue
  useEffect(() => {
    if (toastQueue.length > 0 && !currentToast) {
      const nextToast = toastQueue[0]
      setCurrentToast(nextToast)
      setToastQueue(prev => prev.slice(1))
    }
  }, [toastQueue, currentToast])

  const handleToastClose = () => {
    setCurrentToast(null)
    closeToast()
  }

  // Test notification function for development
  const sendTestNotification = async (type = 'like_post', count = 1) => {
    try {
      const response = await fetch('/api/test-notifications/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ type, count })
      })

      if (!response.ok) {
        throw new Error('Failed to send test notification')
      }

      const result = await response.json()
      console.log('Test notification sent:', result)
      return result
    } catch (error) {
      console.error('Error sending test notification:', error)
      throw error
    }
  }

  const value = {
    // Notification data
    notifications,
    unreadCount,
    loading,
    error,
    
    // Notification actions
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
    refetch,
    getNotificationsByType,
    getUnreadNotifications,
    
    // Toast functionality
    currentToast,
    showToast: !!currentToast,
    closeToast: handleToastClose,
    
    // Development helpers
    sendTestNotification
  }

  return (
    <NotificationContext.Provider value={value}>
      {children}
      
      {/* Toast Notification */}
      {currentToast && (
        <NotificationToast
          notification={currentToast}
          open={!!currentToast}
          onClose={handleToastClose}
          autoHideDuration={6000}
          position={{ vertical: 'top', horizontal: 'right' }}
        />
      )}
    </NotificationContext.Provider>
  )
}

export default NotificationProvider
