import React, { useState } from 'react'
import {
  Card,
  CardHeader,
  CardContent,
  CardActions,
  CardMedia,
  Avatar,
  Typography,
  IconButton,
  Menu,
  MenuItem,
  Chip,
  Box,
  Collapse,
  Divider
} from '@mui/material'
import {
  MoreVert,
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  Bookmark,
  BookmarkBorder,
  Edit,
  Delete,
  Report,
  ExpandMore,
  ExpandLess
} from '@mui/icons-material'
import { useSnackbar } from 'notistack'
import { useAuth } from '../../context/AuthContext'
import { formatDate } from '../../utils'
import LikeButton from './LikeButton'
import ShareButton from './ShareButton'
import CommentSection from './CommentSection'

const PostCard = ({
  post,
  onLike,
  onComment,
  onShare,
  onSave,
  onEdit,
  onDelete,
  onReport,
  showComments = false
}) => {
  const [anchorEl, setAnchorEl] = useState(null)
  const [showAllComments, setShowAllComments] = useState(showComments)
  const [saved, setSaved] = useState(post.isSaved || false)
  const { user } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  const isOwner = user?.id === post.author?.id
  const menuOpen = Boolean(anchorEl)

  const handleMenuOpen = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  const handleSave = async () => {
    try {
      await onSave?.(post.id)
      setSaved(!saved)
      enqueueSnackbar(saved ? 'Post unsaved' : 'Post saved', { variant: 'success' })
    } catch (error) {
      enqueueSnackbar('Failed to save post', { variant: 'error' })
    }
  }

  const handleEdit = () => {
    handleMenuClose()
    onEdit?.(post)
  }

  const handleDelete = () => {
    handleMenuClose()
    onDelete?.(post.id)
  }

  const handleReport = () => {
    handleMenuClose()
    onReport?.(post.id)
  }

  const renderMedia = () => {
    if (!post.media || post.media.length === 0) return null

    const media = post.media[0] // Show first media item

    if (media.type === 'image') {
      return (
        <CardMedia
          component="img"
          height="400"
          image={media.url}
          alt="Post media"
          sx={{
            objectFit: 'cover',
            cursor: 'pointer'
          }}
        />
      )
    }

    if (media.type === 'video') {
      return (
        <CardMedia
          component="video"
          height="400"
          src={media.url}
          controls
          sx={{
            objectFit: 'cover'
          }}
        />
      )
    }

    return null
  }

  const renderMood = () => {
    if (!post.mood) return null

    return (
      <Chip
        size="small"
        label={`Feeling ${post.mood.label}`}
        avatar={<span>{post.mood.emoji}</span>}
        sx={{
          mt: 1,
          bgcolor: post.mood.color + '20',
          color: post.mood.color,
          border: `1px solid ${post.mood.color}40`
        }}
      />
    )
  }

  return (
    <Card
      sx={{
        maxWidth: '100%',
        mb: 2,
        borderRadius: 2,
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        '&:hover': {
          boxShadow: '0 4px 16px rgba(0,0,0,0.15)'
        }
      }}
    >
      <CardHeader
        avatar={
          <Avatar
            src={post.author?.avatar}
            alt={post.author?.name}
            sx={{ width: 40, height: 40 }}
          >
            {post.author?.name?.charAt(0)}
          </Avatar>
        }
        action={
          <IconButton onClick={handleMenuOpen}>
            <MoreVert />
          </IconButton>
        }
        title={
          <Typography variant="subtitle1" fontWeight={600}>
            {post.author?.name}
          </Typography>
        }
        subheader={
          <Typography variant="caption" color="text.secondary">
            {formatDate(post.createdAt)}
          </Typography>
        }
      />

      {post.content && (
        <CardContent sx={{ pt: 0 }}>
          <Typography variant="body1" color="text.primary">
            {post.content}
          </Typography>
          {renderMood()}
          
          {post.tags && post.tags.length > 0 && (
            <Box mt={1} display="flex" flexWrap="wrap" gap={0.5}>
              {post.tags.map((tag, index) => (
                <Chip
                  key={index}
                  label={`#${tag}`}
                  size="small"
                  variant="outlined"
                  clickable
                />
              ))}
            </Box>
          )}
        </CardContent>
      )}

      {renderMedia()}

      <CardActions disableSpacing sx={{ px: 2, py: 1 }}>
        <LikeButton
          liked={post.isLiked}
          count={post.likesCount}
          onLike={() => onLike?.(post.id)}
        />
        
        <IconButton
          onClick={() => setShowAllComments(!showAllComments)}
          sx={{ ml: 1 }}
        >
          <Comment />
        </IconButton>
        <Typography variant="body2" color="text.secondary">
          {post.commentsCount || 0}
        </Typography>

        <ShareButton
          post={post}
          onShare={() => onShare?.(post)}
        />

        <Box sx={{ flexGrow: 1 }} />

        <IconButton onClick={handleSave}>
          {saved ? <Bookmark color="primary" /> : <BookmarkBorder />}
        </IconButton>
      </CardActions>

      <Collapse in={showAllComments} timeout="auto" unmountOnExit>
        <Divider />
        <CommentSection
          postId={post.id}
          onComment={onComment}
        />
      </Collapse>

      <Menu
        anchorEl={anchorEl}
        open={menuOpen}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {isOwner ? [
          <MenuItem key="edit" onClick={handleEdit}>
            <Edit sx={{ mr: 1 }} />
            Edit Post
          </MenuItem>,
          <MenuItem key="delete" onClick={handleDelete}>
            <Delete sx={{ mr: 1 }} />
            Delete Post
          </MenuItem>
        ] : [
          <MenuItem key="report" onClick={handleReport}>
            <Report sx={{ mr: 1 }} />
            Report Post
          </MenuItem>
        ]}
      </Menu>
    </Card>
  )
}

export default PostCard
