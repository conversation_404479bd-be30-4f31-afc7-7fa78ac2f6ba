import React, { useEffect, useState } from 'react'
import {
  Snackbar,
  Alert,
  Avatar,
  Box,
  Typography,
  IconButton,
  Slide
} from '@mui/material'
import { Close, OpenInNew } from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'

const SlideTransition = (props) => {
  return <Slide {...props} direction="down" />
}

const NotificationToast = ({ 
  notification, 
  open, 
  onClose, 
  autoHideDuration = 6000,
  position = { vertical: 'top', horizontal: 'right' }
}) => {
  const navigate = useNavigate()
  const [isVisible, setIsVisible] = useState(open)

  useEffect(() => {
    setIsVisible(open)
  }, [open])

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return
    }
    setIsVisible(false)
    setTimeout(() => onClose && onClose(), 150)
  }

  const handleClick = () => {
    if (notification?.actionUrl) {
      navigate(notification.actionUrl)
    } else {
      // Default navigation based on type
      const { type, data } = notification || {}
      
      if (type?.includes('post') && data?.postId) {
        navigate(`/posts/${data.postId}`)
      } else if (type?.includes('reel') && data?.reelId) {
        navigate(`/reels/${data.reelId}`)
      } else if (type?.includes('follow') && data?.senderId) {
        navigate(`/profile/${data.senderId}`)
      } else if (type?.includes('message')) {
        navigate('/messages')
      }
    }
    handleClose()
  }

  const getSeverity = (priority) => {
    switch (priority) {
      case 'urgent':
        return 'error'
      case 'high':
        return 'warning'
      case 'normal':
        return 'info'
      case 'low':
        return 'success'
      default:
        return 'info'
    }
  }

  if (!notification) return null

  return (
    <Snackbar
      open={isVisible}
      autoHideDuration={autoHideDuration}
      onClose={handleClose}
      anchorOrigin={position}
      TransitionComponent={SlideTransition}
      sx={{
        '& .MuiSnackbar-root': {
          top: '80px !important'
        }
      }}
    >
      <Alert
        severity={getSeverity(notification.priority)}
        onClose={handleClose}
        sx={{
          width: '100%',
          maxWidth: 400,
          cursor: notification.actionUrl ? 'pointer' : 'default',
          '& .MuiAlert-message': {
            width: '100%'
          }
        }}
        onClick={notification.actionUrl ? handleClick : undefined}
        action={
          <Box display="flex" alignItems="center" gap={0.5}>
            {notification.actionUrl && (
              <IconButton
                size="small"
                color="inherit"
                onClick={handleClick}
                title="Open"
              >
                <OpenInNew fontSize="small" />
              </IconButton>
            )}
            <IconButton
              size="small"
              color="inherit"
              onClick={handleClose}
            >
              <Close fontSize="small" />
            </IconButton>
          </Box>
        }
      >
        <Box display="flex" alignItems="center" gap={1.5}>
          {notification.sender?.profilePicture && (
            <Avatar
              src={notification.sender.profilePicture}
              sx={{ width: 32, height: 32 }}
            >
              {notification.sender?.name?.charAt(0)?.toUpperCase()}
            </Avatar>
          )}
          <Box flex={1}>
            <Typography variant="subtitle2" fontWeight="600">
              {notification.title}
            </Typography>
            {notification.message && notification.title !== notification.message && (
              <Typography variant="body2" color="text.secondary">
                {notification.message}
              </Typography>
            )}
          </Box>
        </Box>
      </Alert>
    </Snackbar>
  )
}

export default NotificationToast
