import React from 'react'
import { Box, Button, Typography, useTheme } from '@mui/material'

const ThemeTest = () => {
  const theme = useTheme()

  return (
    <Box sx={{ p: 4 }}>
      <Typography variant="h4" gutterBottom>
        Theme Test
      </Typography>
      
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button variant="contained" color="primary">
          Primary Button
        </Button>
        <Button variant="contained" color="secondary">
          Secondary Button
        </Button>
        <Button variant="contained" color="success">
          Success Button
        </Button>
        <Button variant="contained" color="warning">
          Warning Button
        </Button>
        <Button variant="contained" color="error">
          Error <PERSON>
        </Button>
      </Box>

      <Typography variant="body1">
        Theme mode: {theme.palette.mode}
      </Typography>
      
      <Typography variant="body2">
        Primary main: {theme.palette.primary.main}
      </Typography>
      
      <Typography variant="body2">
        Secondary main: {theme.palette.secondary.main}
      </Typography>
    </Box>
  )
}

export default ThemeTest
