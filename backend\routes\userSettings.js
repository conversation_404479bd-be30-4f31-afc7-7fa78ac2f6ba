const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getUserSettings,
  updateUserSettings,
  updatePassword,
  deleteAccount,
  exportUserData
} = require('../controllers/userSettingsController');

// All routes require authentication
router.use(protect);

// User settings routes
router.route('/')
  .get(getUserSettings)
  .put(updateUserSettings);

// Password management
router.put('/password', updatePassword);

// Account management
router.delete('/account', deleteAccount);

// Data export
router.get('/export-data', exportUserData);

module.exports = router;
