const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const notificationService = require('../services/notificationService');
const User = require('../models/User');

// Protected routes
router.use(protect);

/**
 * Test notification endpoint - creates sample notifications for testing
 */
router.post('/test', async (req, res) => {
  try {
    const { type = 'like_post', count = 1 } = req.body;
    
    // Get current user
    const currentUser = await User.findById(req.user.id).select('username name profilePicture');
    
    const notifications = [];
    
    for (let i = 0; i < count; i++) {
      let notificationData;
      
      switch (type) {
        case 'like_post':
          notificationData = {
            type: 'like_post',
            title: 'New Like',
            message: `${currentUser.name || currentUser.username} liked your post`,
            data: {
              senderId: req.user.id,
              senderName: currentUser.name || currentUser.username,
              senderProfilePicture: currentUser.profilePicture,
              postId: '507f1f77bcf86cd799439011' // Mock post ID
            },
            priority: 'normal',
            actionUrl: '/posts/507f1f77bcf86cd799439011',
            actionText: 'View Post'
          };
          break;
          
        case 'comment_post':
          notificationData = {
            type: 'comment_post',
            title: 'New Comment',
            message: `${currentUser.name || currentUser.username} commented on your post`,
            data: {
              senderId: req.user.id,
              senderName: currentUser.name || currentUser.username,
              senderProfilePicture: currentUser.profilePicture,
              postId: '507f1f77bcf86cd799439011',
              commentText: 'This is a test comment!'
            },
            priority: 'normal',
            actionUrl: '/posts/507f1f77bcf86cd799439011',
            actionText: 'View Comment'
          };
          break;
          
        case 'follow':
          notificationData = {
            type: 'follow',
            title: 'New Follower',
            message: `${currentUser.name || currentUser.username} started following you`,
            data: {
              senderId: req.user.id,
              senderName: currentUser.name || currentUser.username,
              senderProfilePicture: currentUser.profilePicture
            },
            priority: 'normal',
            actionUrl: `/profile/${req.user.id}`,
            actionText: 'View Profile'
          };
          break;
          
        case 'message_received':
          notificationData = {
            type: 'message_received',
            title: 'New Message',
            message: `${currentUser.name || currentUser.username} sent you a message`,
            data: {
              senderId: req.user.id,
              senderName: currentUser.name || currentUser.username,
              senderProfilePicture: currentUser.profilePicture,
              messagePreview: 'Hey! How are you doing?'
            },
            priority: 'high',
            actionUrl: '/messages',
            actionText: 'View Message'
          };
          break;
          
        case 'order_placed':
          notificationData = {
            type: 'order_placed',
            title: 'New Order',
            message: 'You have received a new order',
            data: {
              orderId: '507f1f77bcf86cd799439012',
              orderNumber: 'ORD-2024-001',
              amount: 99.99,
              customerName: currentUser.name || currentUser.username
            },
            priority: 'high',
            actionUrl: '/orders/507f1f77bcf86cd799439012',
            actionText: 'View Order'
          };
          break;
          
        case 'system_update':
          notificationData = {
            type: 'system_update',
            title: 'System Update',
            message: 'New features have been added to the platform',
            data: {
              version: '2.1.0',
              features: ['Real-time notifications', 'Enhanced messaging', 'Better performance']
            },
            priority: 'low',
            actionUrl: '/updates',
            actionText: 'Learn More'
          };
          break;
          
        default:
          notificationData = {
            type: 'admin_announcement',
            title: 'Test Notification',
            message: 'This is a test notification',
            data: {
              testData: true,
              timestamp: new Date()
            },
            priority: 'normal'
          };
      }
      
      // Send notification to current user (for testing)
      const notification = await notificationService.sendNotification(req.user.id, notificationData);
      notifications.push(notification);
    }
    
    res.status(200).json({
      success: true,
      message: `${count} test notification(s) created successfully`,
      data: notifications
    });
    
  } catch (error) {
    console.error('Error creating test notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create test notifications',
      error: error.message
    });
  }
});

/**
 * Test bulk notifications
 */
router.post('/test-bulk', async (req, res) => {
  try {
    const { userIds, message = 'Bulk test notification' } = req.body;
    
    if (!userIds || !Array.isArray(userIds)) {
      return res.status(400).json({
        success: false,
        message: 'userIds array is required'
      });
    }
    
    const notificationData = {
      type: 'admin_announcement',
      title: 'Bulk Notification',
      message,
      data: {
        isBulk: true,
        timestamp: new Date()
      },
      priority: 'normal'
    };
    
    await notificationService.sendBulkNotifications(userIds, notificationData);
    
    res.status(200).json({
      success: true,
      message: `Bulk notification sent to ${userIds.length} users`,
      data: { userIds, notificationData }
    });
    
  } catch (error) {
    console.error('Error sending bulk notifications:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send bulk notifications',
      error: error.message
    });
  }
});

/**
 * Get notification types for testing
 */
router.get('/types', (req, res) => {
  const types = [
    'like_post',
    'like_reel', 
    'like_comment',
    'comment_post',
    'comment_reel',
    'follow',
    'message_received',
    'order_placed',
    'order_confirmed',
    'system_update',
    'admin_announcement'
  ];
  
  res.status(200).json({
    success: true,
    data: types
  });
});

module.exports = router;
