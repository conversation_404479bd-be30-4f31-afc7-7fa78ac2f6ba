const User = require('../models/User');
const NotificationPreference = require('../models/NotificationPreference');
const { createError } = require('../utils/error');
const bcrypt = require('bcryptjs');

/**
 * @desc    Get user settings
 * @route   GET /api/users/settings
 * @access  Private
 */
exports.getUserSettings = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    
    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Get notification preferences
    let notificationPreferences = await NotificationPreference.findOne({ user: req.user.id });
    
    if (!notificationPreferences) {
      // Create default notification preferences
      notificationPreferences = await NotificationPreference.create({
        user: req.user.id
      });
    }

    // Construct settings object
    const settings = {
      profile: {
        name: user.name,
        username: user.username,
        email: user.email,
        bio: user.bio,
        location: user.location,
        website: user.website,
        profilePicture: user.profilePicture,
        isPrivate: user.isPrivate
      },
      notifications: {
        email: notificationPreferences.channels?.email ?? true,
        push: notificationPreferences.channels?.push ?? true,
        inApp: notificationPreferences.channels?.inApp ?? true,
        sms: notificationPreferences.channels?.sms ?? false,
        likes: notificationPreferences.social?.newFollowers ?? true,
        comments: notificationPreferences.social?.commentReplies ?? true,
        follows: notificationPreferences.social?.newFollowers ?? true,
        messages: notificationPreferences.social?.messageRequests ?? true,
        liveStreams: notificationPreferences.liveStream?.followedCreatorStart ?? true,
        quietHours: notificationPreferences.quietHours || {
          enabled: false,
          start: '22:00',
          end: '08:00',
          timezone: 'UTC'
        }
      },
      privacy: {
        profileVisibility: user.isPrivate ? 'private' : 'public',
        showEmail: user.showEmail || false,
        showPhone: user.showPhone || false,
        allowMessages: user.allowMessages || 'everyone',
        allowTagging: user.allowTagging !== false,
        allowMentions: user.allowMentions !== false,
        showOnlineStatus: user.showOnlineStatus !== false,
        allowLocationTracking: user.allowLocationTracking || false
      },
      preferences: {
        language: user.language || 'en',
        timezone: user.timezone || 'UTC',
        dateFormat: user.dateFormat || 'MM/DD/YYYY',
        timeFormat: user.timeFormat || '12h',
        currency: user.currency || 'USD',
        autoPlayVideos: user.autoPlayVideos !== false,
        showSensitiveContent: user.showSensitiveContent || false
      },
      theme: user.themePreference || {
        mode: 'light',
        primaryColor: '#1976d2',
        accentColor: '#e91e63'
      },
      wellness: user.wellnessSettings || {
        contentFilters: {
          hideNegativeContent: false,
          preferredEmotions: [],
          avoidedEmotions: []
        },
        timeLimit: {
          enabled: false,
          dailyLimitMinutes: 60
        },
        hideMetrics: false,
        wellnessReminders: {
          enabled: true,
          frequency: 'every2hours'
        }
      },
      security: {
        twoFactorEnabled: user.twoFactorEnabled || false,
        loginAlerts: user.loginAlerts !== false,
        sessionTimeout: user.sessionTimeout || 30,
        allowedDevices: user.allowedDevices || 'unlimited'
      }
    };

    res.status(200).json({
      success: true,
      data: settings
    });

  } catch (error) {
    console.error('Error getting user settings:', error);
    next(error);
  }
};

/**
 * @desc    Update user settings
 * @route   PUT /api/users/settings
 * @access  Private
 */
exports.updateUserSettings = async (req, res, next) => {
  try {
    const { profile, notifications, privacy, preferences, theme, wellness, security } = req.body;
    
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Update profile settings
    if (profile) {
      const allowedProfileFields = ['name', 'bio', 'location', 'website', 'isPrivate'];
      allowedProfileFields.forEach(field => {
        if (profile[field] !== undefined) {
          user[field] = profile[field];
        }
      });
    }

    // Update privacy settings
    if (privacy) {
      const privacyFields = [
        'showEmail', 'showPhone', 'allowMessages', 'allowTagging', 
        'allowMentions', 'showOnlineStatus', 'allowLocationTracking'
      ];
      privacyFields.forEach(field => {
        if (privacy[field] !== undefined) {
          user[field] = privacy[field];
        }
      });
      
      if (privacy.profileVisibility) {
        user.isPrivate = privacy.profileVisibility === 'private';
      }
    }

    // Update preferences
    if (preferences) {
      const preferenceFields = [
        'language', 'timezone', 'dateFormat', 'timeFormat', 'currency',
        'autoPlayVideos', 'showSensitiveContent'
      ];
      preferenceFields.forEach(field => {
        if (preferences[field] !== undefined) {
          user[field] = preferences[field];
        }
      });
    }

    // Update theme
    if (theme) {
      user.themePreference = {
        ...user.themePreference,
        ...theme
      };
    }

    // Update wellness settings
    if (wellness) {
      user.wellnessSettings = {
        ...user.wellnessSettings,
        ...wellness
      };
    }

    // Update security settings
    if (security) {
      const securityFields = ['loginAlerts', 'sessionTimeout', 'allowedDevices'];
      securityFields.forEach(field => {
        if (security[field] !== undefined) {
          user[field] = security[field];
        }
      });
    }

    await user.save();

    // Update notification preferences
    if (notifications) {
      let notificationPrefs = await NotificationPreference.findOne({ user: req.user.id });
      
      if (!notificationPrefs) {
        notificationPrefs = new NotificationPreference({ user: req.user.id });
      }

      // Update channels
      if (notifications.email !== undefined) notificationPrefs.channels.email = notifications.email;
      if (notifications.push !== undefined) notificationPrefs.channels.push = notifications.push;
      if (notifications.inApp !== undefined) notificationPrefs.channels.inApp = notifications.inApp;
      if (notifications.sms !== undefined) notificationPrefs.channels.sms = notifications.sms;

      // Update social preferences
      if (notifications.likes !== undefined) notificationPrefs.social.newFollowers = notifications.likes;
      if (notifications.comments !== undefined) notificationPrefs.social.commentReplies = notifications.comments;
      if (notifications.follows !== undefined) notificationPrefs.social.newFollowers = notifications.follows;
      if (notifications.messages !== undefined) notificationPrefs.social.messageRequests = notifications.messages;

      // Update live stream preferences
      if (notifications.liveStreams !== undefined) {
        notificationPrefs.liveStream.followedCreatorStart = notifications.liveStreams;
      }

      // Update quiet hours
      if (notifications.quietHours) {
        notificationPrefs.quietHours = {
          ...notificationPrefs.quietHours,
          ...notifications.quietHours
        };
      }

      await notificationPrefs.save();
    }

    res.status(200).json({
      success: true,
      message: 'Settings updated successfully',
      data: {
        profile: user.name,
        updated: new Date()
      }
    });

  } catch (error) {
    console.error('Error updating user settings:', error);
    next(error);
  }
};

/**
 * @desc    Update password
 * @route   PUT /api/users/password
 * @access  Private
 */
exports.updatePassword = async (req, res, next) => {
  try {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
      return next(createError(400, 'Please provide current and new password'));
    }

    if (newPassword.length < 6) {
      return next(createError(400, 'New password must be at least 6 characters'));
    }

    const user = await User.findById(req.user.id).select('+password');

    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Check current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    
    if (!isCurrentPasswordValid) {
      return next(createError(400, 'Current password is incorrect'));
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    user.password = await bcrypt.hash(newPassword, salt);
    
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Password updated successfully'
    });

  } catch (error) {
    console.error('Error updating password:', error);
    next(error);
  }
};

/**
 * @desc    Delete user account
 * @route   DELETE /api/users/account
 * @access  Private
 */
exports.deleteAccount = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Soft delete - mark as deleted instead of removing
    user.isDeleted = true;
    user.deletedAt = new Date();
    user.email = `deleted_${user.email}`;
    user.username = `deleted_${user.username}`;
    
    await user.save();

    // Also delete notification preferences
    await NotificationPreference.deleteOne({ user: req.user.id });

    res.status(200).json({
      success: true,
      message: 'Account deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting account:', error);
    next(error);
  }
};

/**
 * @desc    Export user data
 * @route   GET /api/users/export-data
 * @access  Private
 */
exports.exportUserData = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id).select('-password');
    const notificationPrefs = await NotificationPreference.findOne({ user: req.user.id });

    const exportData = {
      profile: user,
      notificationPreferences: notificationPrefs,
      exportDate: new Date(),
      dataVersion: '1.0'
    };

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="${user.username}_data.json"`);
    
    res.status(200).json(exportData);

  } catch (error) {
    console.error('Error exporting user data:', error);
    next(error);
  }
};
