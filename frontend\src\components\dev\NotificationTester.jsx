import React, { useState } from 'react'
import {
  Box,
  Button,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  Paper,
  Grid,
  Chip,
  Alert
} from '@mui/material'
import { useNotificationContext } from '../../context/NotificationContext'

const NotificationTester = () => {
  const [selectedType, setSelectedType] = useState('like_post')
  const [count, setCount] = useState(1)
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  
  const { sendTestNotification, unreadCount, notifications } = useNotificationContext()

  const notificationTypes = [
    { value: 'like_post', label: 'Like Post' },
    { value: 'like_reel', label: 'Like Reel' },
    { value: 'comment_post', label: 'Comment on Post' },
    { value: 'comment_reel', label: 'Comment on Reel' },
    { value: 'follow', label: 'New Follower' },
    { value: 'message_received', label: 'New Message' },
    { value: 'order_placed', label: 'New Order' },
    { value: 'system_update', label: 'System Update' }
  ]

  const handleSendTest = async () => {
    setLoading(true)
    setMessage('')
    
    try {
      await sendTestNotification(selectedType, count)
      setMessage(`Successfully sent ${count} test notification(s) of type "${selectedType}"`)
    } catch (error) {
      setMessage(`Error: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h5" gutterBottom>
        🔔 Notification System Tester
      </Typography>
      
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Test the real-time notification system. This will send notifications to your account.
      </Typography>

      {/* Current Stats */}
      <Box sx={{ mb: 3 }}>
        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Chip 
              label={`Unread: ${unreadCount}`} 
              color="primary" 
              variant="outlined"
            />
          </Grid>
          <Grid item xs={6}>
            <Chip 
              label={`Total: ${notifications.length}`} 
              color="secondary" 
              variant="outlined"
            />
          </Grid>
        </Grid>
      </Box>

      {/* Test Controls */}
      <Grid container spacing={2} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Notification Type</InputLabel>
            <Select
              value={selectedType}
              label="Notification Type"
              onChange={(e) => setSelectedType(e.target.value)}
            >
              {notificationTypes.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  {type.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        
        <Grid item xs={12} md={3}>
          <TextField
            fullWidth
            type="number"
            label="Count"
            value={count}
            onChange={(e) => setCount(Math.max(1, Math.min(10, parseInt(e.target.value) || 1)))}
            inputProps={{ min: 1, max: 10 }}
          />
        </Grid>
        
        <Grid item xs={12} md={3}>
          <Button
            fullWidth
            variant="contained"
            onClick={handleSendTest}
            disabled={loading}
            sx={{ height: '56px' }}
          >
            {loading ? 'Sending...' : 'Send Test'}
          </Button>
        </Grid>
      </Grid>

      {/* Message */}
      {message && (
        <Alert 
          severity={message.includes('Error') ? 'error' : 'success'}
          sx={{ mb: 2 }}
        >
          {message}
        </Alert>
      )}

      {/* Quick Actions */}
      <Box>
        <Typography variant="h6" gutterBottom>
          Quick Tests
        </Typography>
        <Grid container spacing={1}>
          <Grid item>
            <Button
              size="small"
              variant="outlined"
              onClick={() => {
                setSelectedType('like_post')
                setCount(1)
                handleSendTest()
              }}
            >
              Like Notification
            </Button>
          </Grid>
          <Grid item>
            <Button
              size="small"
              variant="outlined"
              onClick={() => {
                setSelectedType('follow')
                setCount(1)
                handleSendTest()
              }}
            >
              Follow Notification
            </Button>
          </Grid>
          <Grid item>
            <Button
              size="small"
              variant="outlined"
              onClick={() => {
                setSelectedType('message_received')
                setCount(1)
                handleSendTest()
              }}
            >
              Message Notification
            </Button>
          </Grid>
          <Grid item>
            <Button
              size="small"
              variant="outlined"
              onClick={() => {
                setSelectedType('system_update')
                setCount(1)
                handleSendTest()
              }}
            >
              System Notification
            </Button>
          </Grid>
        </Grid>
      </Box>

      {/* Instructions */}
      <Box sx={{ mt: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
        <Typography variant="subtitle2" gutterBottom>
          How to test:
        </Typography>
        <Typography variant="body2" component="ul" sx={{ pl: 2 }}>
          <li>Select a notification type from the dropdown</li>
          <li>Set the number of notifications to send (1-10)</li>
          <li>Click "Send Test" to trigger notifications</li>
          <li>Check the notification dropdown in the header</li>
          <li>Look for toast notifications in the top-right corner</li>
          <li>Visit the Notifications page to see the full list</li>
        </Typography>
      </Box>
    </Paper>
  )
}

export default NotificationTester
