const express = require('express');
const { createServer } = require('http');
const { Server } = require('socket.io');
const cors = require('cors');

const app = express();
const httpServer = createServer(app);

// Basic CORS
app.use(cors({
  origin: [
    'http://localhost:50000',
    'http://localhost:50001',
    'http://localhost:50002',
    'http://localhost:50003',
    'http://localhost:50004',
    'http://localhost:3000',
    'http://localhost:3001'
  ],
  credentials: true
}));

app.use(express.json());

// Basic Socket.IO setup
const io = new Server(httpServer, {
  cors: {
    origin: [
      'http://localhost:50000',
      'http://localhost:50001',
      'http://localhost:50002',
      'http://localhost:50003',
      'http://localhost:50004',
      'http://localhost:3000',
      'http://localhost:3001'
    ],
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket', 'polling']
});

// Basic routes
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Test server running' });
});

// Basic Socket.IO connection
io.on('connection', (socket) => {
  console.log('User connected:', socket.id);
  
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
  });
});

const PORT = process.env.PORT || 10001;

httpServer.listen(PORT, () => {
  console.log(`✅ Test server running on port ${PORT}`);
  console.log(`✅ Socket.IO server initialized`);
});
