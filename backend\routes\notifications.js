const express = require('express');
const router = express.Router();
const { protect } = require('../middleware/auth');
const {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  deleteAllNotifications,
  createNotification,
} = require('../controllers/notificationController');

// Routes that require authentication
router.use(protect);

// Get all notifications for the authenticated user
router.get('/', getNotifications);

// Get unread notifications count
router.get('/unread-count', getUnreadCount);

// Mark all notifications as read
router.put('/read-all', markAllAsRead);

// Delete all notifications
router.delete('/all', deleteAllNotifications);

// Clear all notifications (alias for delete all)
router.delete('/clear-all', deleteAllNotifications);

// Create a notification (for testing purposes)
router.post('/', createNotification);

// Mark a notification as read
router.put('/:id/read', markAsR<PERSON>);

// Delete a notification
router.delete('/:id', deleteNotification);

module.exports = router;
