import React from 'react'
import { motion } from 'framer-motion'
import { Box, Typography, useTheme, useMediaQuery } from '@mui/material'

const AuthToggle = ({ isLogin, onToggle }) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  return (
    <Box sx={{ mb: isMobile ? 3 : 4, textAlign: 'center' }}>
      <Typography
        variant={isMobile ? "h5" : "h4"}
        component="h1"
        sx={{
          fontWeight: 'bold',
          mb: 1,
          fontSize: isMobile ? '1.5rem' : '2rem',
          background: theme.palette.mode === 'dark'
            ? 'linear-gradient(45deg, #fff 30%, #e0e0e0 90%)'
            : 'linear-gradient(45deg, #333 30%, #666 90%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text'
        }}
      >
        {isLogin ? 'Welcome Back' : 'Create Account'}
      </Typography>

      <Typography
        variant="body1"
        color="text.secondary"
        sx={{
          mb: isMobile ? 2 : 3,
          fontSize: isMobile ? '0.875rem' : '1rem',
          px: isMobile ? 1 : 0
        }}
      >
        {isLogin
          ? 'Sign in to continue your journey'
          : 'Join our amazing community today'
        }
      </Typography>

      {/* Toggle Buttons */}
      <Box
        sx={{
          display: 'flex',
          background: theme.palette.mode === 'dark'
            ? 'rgba(255, 255, 255, 0.05)'
            : 'rgba(0, 0, 0, 0.05)',
          borderRadius: 3,
          p: 0.5,
          position: 'relative',
          border: `1px solid ${theme.palette.divider}`
        }}
      >
        {/* Sliding background */}
        <motion.div
          layout
          initial={false}
          animate={{
            x: isLogin ? 0 : '100%'
          }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30
          }}
          style={{
            position: 'absolute',
            top: '4px',
            left: '4px',
            right: '4px',
            bottom: '4px',
            width: 'calc(50% - 4px)',
            background: theme.palette.mode === 'dark'
              ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
              : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '10px',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
            zIndex: 1
          }}
        />

        {/* Login Button */}
        <motion.button
          onClick={() => onToggle()}
          disabled={isLogin}
          whileHover={{ scale: isLogin ? 1 : 1.02 }}
          whileTap={{ scale: 0.98 }}
          style={{
            flex: 1,
            padding: isMobile ? '10px 16px' : '12px 24px',
            border: 'none',
            background: 'transparent',
            color: isLogin ? 'white' : theme.palette.text.secondary,
            fontWeight: isLogin ? 600 : 500,
            fontSize: isMobile ? '0.8rem' : '0.875rem',
            borderRadius: '10px',
            cursor: isLogin ? 'default' : 'pointer',
            position: 'relative',
            zIndex: 2,
            transition: 'color 0.2s ease'
          }}
        >
          Sign In
        </motion.button>

        {/* Register Button */}
        <motion.button
          onClick={() => onToggle()}
          disabled={!isLogin}
          whileHover={{ scale: !isLogin ? 1 : 1.02 }}
          whileTap={{ scale: 0.98 }}
          style={{
            flex: 1,
            padding: isMobile ? '10px 16px' : '12px 24px',
            border: 'none',
            background: 'transparent',
            color: !isLogin ? 'white' : theme.palette.text.secondary,
            fontWeight: !isLogin ? 600 : 500,
            fontSize: isMobile ? '0.8rem' : '0.875rem',
            borderRadius: '10px',
            cursor: !isLogin ? 'default' : 'pointer',
            position: 'relative',
            zIndex: 2,
            transition: 'color 0.2s ease'
          }}
        >
          Sign Up
        </motion.button>
      </Box>
    </Box>
  )
}

export default AuthToggle
