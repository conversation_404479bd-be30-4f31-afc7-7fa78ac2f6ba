import React from 'react'
import { motion } from 'framer-motion'
import {
  Box,
  TextField,
  Button,
  Typography,
  InputAdornment,
  IconButton,
  Checkbox,
  FormControlLabel,
  Link,
  useTheme,
  useMediaQuery,
  Chip
} from '@mui/material'
import {
  Visibility,
  VisibilityOff,
  Person,
  AlternateEmail,
  Email,
  Lock,
  PersonAdd,
  Check,
  Close,
  HourglassEmpty
} from '@mui/icons-material'
import { useAuthForm } from '../../hooks/useAuthForm'
import { registerSchema } from '../../utils/validation'
import PasswordStrength from './PasswordStrength'

const RegisterForm = ({ onSuccess }) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  const {
    formik,
    isSubmitting,
    showPassword,
    showConfirmPassword,
    usernameStatus,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    handleUsernameChange
  } = useAuthForm(
    {
      name: '',
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      agreeToTerms: false
    },
    registerSchema,
    onSuccess
  )

  const getUsernameIcon = () => {
    if (usernameStatus.checking) return <HourglassEmpty sx={{ color: theme.palette.warning.main }} />
    if (usernameStatus.available === true) return <Check sx={{ color: theme.palette.success.main }} />
    if (usernameStatus.available === false) return <Close sx={{ color: theme.palette.error.main }} />
    return <AlternateEmail sx={{ color: theme.palette.text.secondary }} />
  }

  const getUsernameColor = () => {
    if (usernameStatus.checking) return theme.palette.warning.main
    if (usernameStatus.available === true) return theme.palette.success.main
    if (usernameStatus.available === false) return theme.palette.error.main
    return 'inherit'
  }

  return (
    <Box component="form" onSubmit={formik.handleSubmit} noValidate>
      {/* Full Name Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <TextField
          fullWidth
          name="name"
          label="Full Name"
          type="text"
          value={formik.values.name}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.name && Boolean(formik.errors.name)}
          helperText={formik.touched.name && formik.errors.name}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Person sx={{ color: theme.palette.text.secondary }} />
              </InputAdornment>
            )
          }}
          sx={{
            mb: isMobile ? 2.5 : 3,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              fontSize: isMobile ? '0.875rem' : '1rem',
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}20`
              },
              '&.Mui-focused': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}30`
              }
            }
          }}
        />
      </motion.div>

      {/* Username Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <TextField
          fullWidth
          name="username"
          label="Username"
          type="text"
          value={formik.values.username}
          onChange={handleUsernameChange}
          onBlur={formik.handleBlur}
          error={formik.touched.username && Boolean(formik.errors.username)}
          helperText={
            formik.touched.username && formik.errors.username
              ? formik.errors.username
              : usernameStatus.message
          }
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                {getUsernameIcon()}
              </InputAdornment>
            ),
            endAdornment: usernameStatus.available !== null && (
              <InputAdornment position="end">
                <Chip
                  size="small"
                  label={usernameStatus.available ? 'Available' : 'Taken'}
                  sx={{
                    backgroundColor: usernameStatus.available
                      ? `${theme.palette.success.main}20`
                      : `${theme.palette.error.main}20`,
                    color: usernameStatus.available
                      ? theme.palette.success.main
                      : theme.palette.error.main,
                    fontSize: '0.75rem'
                  }}
                />
              </InputAdornment>
            )
          }}
          sx={{
            mb: isMobile ? 2.5 : 3,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              fontSize: isMobile ? '0.875rem' : '1rem',
              borderColor: getUsernameColor(),
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${getUsernameColor()}20`
              },
              '&.Mui-focused': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${getUsernameColor()}30`
              }
            },
            '& .MuiFormHelperText-root': {
              color: getUsernameColor()
            }
          }}
        />
      </motion.div>

      {/* Email Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <TextField
          fullWidth
          name="email"
          label="Email Address"
          type="email"
          value={formik.values.email}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.email && Boolean(formik.errors.email)}
          helperText={formik.touched.email && formik.errors.email}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Email sx={{ color: theme.palette.text.secondary }} />
              </InputAdornment>
            )
          }}
          sx={{
            mb: 3,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}20`
              },
              '&.Mui-focused': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}30`
              }
            }
          }}
        />
      </motion.div>

      {/* Password Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <TextField
          fullWidth
          name="password"
          label="Password"
          type={showPassword ? 'text' : 'password'}
          value={formik.values.password}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.password && Boolean(formik.errors.password)}
          helperText={formik.touched.password && formik.errors.password}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Lock sx={{ color: theme.palette.text.secondary }} />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={togglePasswordVisibility}
                  edge="end"
                  sx={{ color: theme.palette.text.secondary }}
                >
                  {showPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            )
          }}
          sx={{
            mb: 1,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}20`
              },
              '&.Mui-focused': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}30`
              }
            }
          }}
        />

        {/* Password Strength Indicator */}
        <PasswordStrength password={formik.values.password} />
      </motion.div>

      {/* Confirm Password Field */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
      >
        <TextField
          fullWidth
          name="confirmPassword"
          label="Confirm Password"
          type={showConfirmPassword ? 'text' : 'password'}
          value={formik.values.confirmPassword}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
          helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Lock sx={{ color: theme.palette.text.secondary }} />
              </InputAdornment>
            ),
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={toggleConfirmPasswordVisibility}
                  edge="end"
                  sx={{ color: theme.palette.text.secondary }}
                >
                  {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                </IconButton>
              </InputAdornment>
            )
          }}
          sx={{
            mb: 3,
            '& .MuiOutlinedInput-root': {
              borderRadius: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}20`
              },
              '&.Mui-focused': {
                transform: 'translateY(-1px)',
                boxShadow: `0 4px 12px ${theme.palette.primary.main}30`
              }
            }
          }}
        />
      </motion.div>

      {/* Terms Agreement */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
      >
        <FormControlLabel
          control={
            <Checkbox
              name="agreeToTerms"
              checked={formik.values.agreeToTerms}
              onChange={formik.handleChange}
              sx={{
                color: theme.palette.primary.main,
                '&.Mui-checked': {
                  color: theme.palette.primary.main
                }
              }}
            />
          }
          label={
            <Typography variant="body2" sx={{ fontSize: isMobile ? '0.8rem' : '0.875rem' }}>
              I agree to the{' '}
              <Link
                href="/terms"
                target="_blank"
                sx={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  fontWeight: 500,
                  '&:hover': { textDecoration: 'underline' }
                }}
              >
                Terms of Service
              </Link>
              {' '}and{' '}
              <Link
                href="/privacy"
                target="_blank"
                sx={{
                  color: theme.palette.primary.main,
                  textDecoration: 'none',
                  fontWeight: 500,
                  '&:hover': { textDecoration: 'underline' }
                }}
              >
                Privacy Policy
              </Link>
            </Typography>
          }
          sx={{ mb: 3 }}
        />
        {formik.touched.agreeToTerms && formik.errors.agreeToTerms && (
          <Typography
            variant="body2"
            sx={{
              color: theme.palette.error.main,
              fontSize: '0.75rem',
              mt: -2,
              mb: 2,
              ml: 4
            }}
          >
            {formik.errors.agreeToTerms}
          </Typography>
        )}
      </motion.div>

      {/* Submit Button */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <Button
          type="submit"
          fullWidth
          variant="contained"
          size="large"
          disabled={isSubmitting || !formik.values.agreeToTerms}
          startIcon={!isSubmitting && <PersonAdd />}
          sx={{
            py: isMobile ? 1.2 : 1.5,
            borderRadius: 2,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            fontWeight: 600,
            fontSize: isMobile ? '0.9rem' : '1rem',
            textTransform: 'none',
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.4)',
            '&:hover': {
              background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 16px rgba(102, 126, 234, 0.5)'
            },
            '&:disabled': {
              background: theme.palette.action.disabledBackground,
              color: theme.palette.action.disabled
            },
            transition: 'all 0.2s ease'
          }}
        >
          {isSubmitting ? (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              style={{
                width: 20,
                height: 20,
                border: '2px solid transparent',
                borderTop: '2px solid currentColor',
                borderRadius: '50%'
              }}
            />
          ) : (
            'Create Account'
          )}
        </Button>
      </motion.div>

      {/* Registration Benefits */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <Box sx={{ mt: 3 }}>
          {/* Account Benefits */}
          <Box
            sx={{
              p: 2,
              backgroundColor: theme.palette.mode === 'dark'
                ? 'rgba(76, 175, 80, 0.1)'
                : 'rgba(76, 175, 80, 0.05)',
              borderRadius: 2,
              border: `1px solid ${theme.palette.success.main}30`,
              mb: 2
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: theme.palette.success.main,
                fontSize: '0.75rem',
                fontWeight: 600,
                mb: 1,
                display: 'flex',
                alignItems: 'center',
                gap: 1
              }}
            >
              <span>🎉</span> Join Our Community
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              {[
                'Connect with like-minded people',
                'Share your thoughts and stories',
                'Discover trending conversations',
                'Customize your experience'
              ].map((benefit, index) => (
                <Typography
                  key={index}
                  variant="body2"
                  sx={{
                    fontSize: '0.7rem',
                    color: theme.palette.text.secondary,
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  <span style={{ color: theme.palette.success.main }}>✓</span>
                  {benefit}
                </Typography>
              ))}
            </Box>
          </Box>

          {/* Security Notice */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: 1,
              p: 2,
              backgroundColor: theme.palette.mode === 'dark'
                ? 'rgba(255, 255, 255, 0.05)'
                : 'rgba(0, 0, 0, 0.02)',
              borderRadius: 2,
              border: `1px solid ${theme.palette.divider}`
            }}
          >
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: theme.palette.success.main,
                animation: 'pulse 2s infinite'
              }}
            />
            <Typography
              variant="body2"
              sx={{
                fontSize: '0.75rem',
                color: theme.palette.text.secondary
              }}
            >
              Your data is protected with end-to-end encryption
            </Typography>
          </Box>
        </Box>
      </motion.div>
    </Box>
  )
}

export default RegisterForm
