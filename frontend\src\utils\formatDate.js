import { formatDistanceToNow, format, isToday, isYesterday, isThisWeek, isThisYear } from 'date-fns'

/**
 * Format date for social media posts
 * @param {string|Date} date - The date to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted date string
 */
const formatDate = (date, options = {}) => {
  const {
    relative = true,
    includeTime = false,
    short = false
  } = options

  if (!date) return ''

  const dateObj = new Date(date)
  
  if (isNaN(dateObj.getTime())) {
    return 'Invalid date'
  }

  // For relative formatting (default for social media)
  if (relative) {
    const now = new Date()
    const diffInMinutes = Math.floor((now - dateObj) / (1000 * 60))
    
    // Less than 1 minute ago
    if (diffInMinutes < 1) {
      return 'Just now'
    }
    
    // Less than 1 hour ago
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`
    }
    
    // Less than 24 hours ago
    if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60)
      return `${hours}h ago`
    }
    
    // Less than 7 days ago
    if (diffInMinutes < 10080) {
      const days = Math.floor(diffInMinutes / 1440)
      return `${days}d ago`
    }
    
    // More than 7 days ago, show actual date
    if (isThisYear(dateObj)) {
      return format(dateObj, 'MMM d')
    } else {
      return format(dateObj, 'MMM d, yyyy')
    }
  }

  // For absolute formatting
  if (isToday(dateObj)) {
    return includeTime ? format(dateObj, 'h:mm a') : 'Today'
  }
  
  if (isYesterday(dateObj)) {
    return includeTime ? `Yesterday at ${format(dateObj, 'h:mm a')}` : 'Yesterday'
  }
  
  if (isThisWeek(dateObj)) {
    return includeTime ? format(dateObj, 'EEEE \'at\' h:mm a') : format(dateObj, 'EEEE')
  }
  
  if (isThisYear(dateObj)) {
    return includeTime ? format(dateObj, 'MMM d \'at\' h:mm a') : format(dateObj, 'MMM d')
  }
  
  return includeTime ? format(dateObj, 'MMM d, yyyy \'at\' h:mm a') : format(dateObj, 'MMM d, yyyy')
}

/**
 * Format date for chat messages
 * @param {string|Date} date - The date to format
 * @returns {string} Formatted date string
 */
export const formatChatDate = (date) => {
  if (!date) return ''
  
  const dateObj = new Date(date)
  
  if (isToday(dateObj)) {
    return format(dateObj, 'h:mm a')
  }
  
  if (isYesterday(dateObj)) {
    return 'Yesterday'
  }
  
  if (isThisWeek(dateObj)) {
    return format(dateObj, 'EEEE')
  }
  
  if (isThisYear(dateObj)) {
    return format(dateObj, 'MMM d')
  }
  
  return format(dateObj, 'MMM d, yyyy')
}

/**
 * Format date for detailed views
 * @param {string|Date} date - The date to format
 * @returns {string} Formatted date string
 */
export const formatDetailedDate = (date) => {
  if (!date) return ''
  
  const dateObj = new Date(date)
  return format(dateObj, 'MMMM d, yyyy \'at\' h:mm a')
}

/**
 * Get time ago string
 * @param {string|Date} date - The date to format
 * @returns {string} Time ago string
 */
export const getTimeAgo = (date) => {
  if (!date) return ''
  
  const dateObj = new Date(date)
  return formatDistanceToNow(dateObj, { addSuffix: true })
}

export default formatDate
