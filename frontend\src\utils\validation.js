import * as yup from 'yup'

// Password strength validation
export const passwordStrengthRegex = {
  minLength: /.{8,}/,
  hasUpperCase: /[A-Z]/,
  hasLowerCase: /[a-z]/,
  hasNumbers: /\d/,
  hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/
}

export const getPasswordStrength = (password) => {
  if (!password) return { score: 0, feedback: [] }

  const checks = [
    { regex: passwordStrengthRegex.minLength, message: 'At least 8 characters' },
    { regex: passwordStrengthRegex.hasUpperCase, message: 'One uppercase letter' },
    { regex: passwordStrengthRegex.hasLowerCase, message: 'One lowercase letter' },
    { regex: passwordStrengthRegex.hasNumbers, message: 'One number' },
    { regex: passwordStrengthRegex.hasSpecialChar, message: 'One special character' }
  ]

  const passed = checks.filter(check => check.regex.test(password))
  const failed = checks.filter(check => !check.regex.test(password))

  let score = passed.length
  let strength = 'Very Weak'
  let color = '#f44336'

  if (score >= 4) {
    strength = 'Strong'
    color = '#4caf50'
  } else if (score >= 3) {
    strength = 'Good'
    color = '#ff9800'
  } else if (score >= 2) {
    strength = 'Fair'
    color = '#ff5722'
  } else if (score >= 1) {
    strength = 'Weak'
    color = '#f44336'
  }

  return {
    score,
    strength,
    color,
    percentage: (score / checks.length) * 100,
    feedback: failed.map(check => check.message)
  }
}

// Username validation
export const validateUsername = async (username) => {
  if (!username) return { isValid: false, message: 'Username is required' }
  
  if (username.length < 3) {
    return { isValid: false, message: 'Username must be at least 3 characters' }
  }
  
  if (username.length > 20) {
    return { isValid: false, message: 'Username cannot exceed 20 characters' }
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    return { isValid: false, message: 'Username can only contain letters, numbers, and underscores' }
  }

  // TODO: Check username availability with backend
  // const response = await checkUsernameAvailability(username)
  // if (!response.available) {
  //   return { isValid: false, message: 'Username is already taken' }
  // }

  return { isValid: true, message: 'Username is available' }
}

// Login form validation schema
export const loginSchema = yup.object({
  email: yup
    .string()
    .required('Email is required')
    .test('email-or-username', 'Enter a valid email or username', function(value) {
      if (!value) return false
      
      // Check if it's an email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (emailRegex.test(value)) return true
      
      // Check if it's a valid username
      const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
      return usernameRegex.test(value)
    }),
  password: yup
    .string()
    .required('Password is required')
    .min(6, 'Password must be at least 6 characters'),
  rememberMe: yup.boolean()
})

// Register form validation schema
export const registerSchema = yup.object({
  name: yup
    .string()
    .required('Full name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(50, 'Name cannot exceed 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Name can only contain letters and spaces'),
  username: yup
    .string()
    .required('Username is required')
    .min(3, 'Username must be at least 3 characters')
    .max(20, 'Username cannot exceed 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  email: yup
    .string()
    .required('Email is required')
    .email('Enter a valid email address'),
  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .test('password-strength', 'Password is too weak', function(value) {
      if (!value) return false
      const strength = getPasswordStrength(value)
      return strength.score >= 3 // Require at least "Good" strength
    }),
  confirmPassword: yup
    .string()
    .required('Please confirm your password')
    .oneOf([yup.ref('password')], 'Passwords must match'),
  agreeToTerms: yup
    .boolean()
    .oneOf([true], 'You must agree to the terms and conditions')
})

// Forgot password validation schema
export const forgotPasswordSchema = yup.object({
  email: yup
    .string()
    .required('Email is required')
    .email('Enter a valid email address')
})

// Reset password validation schema
export const resetPasswordSchema = yup.object({
  password: yup
    .string()
    .required('Password is required')
    .min(8, 'Password must be at least 8 characters')
    .test('password-strength', 'Password is too weak', function(value) {
      if (!value) return false
      const strength = getPasswordStrength(value)
      return strength.score >= 3
    }),
  confirmPassword: yup
    .string()
    .required('Please confirm your password')
    .oneOf([yup.ref('password')], 'Passwords must match')
})

export default {
  loginSchema,
  registerSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  getPasswordStrength,
  validateUsername
}
