// Storage keys for localStorage
export const STORAGE_KEYS = {
  THEME: 'letstalk_theme',
  AUTH_TOKEN: 'letstalk_token',
  USER_DATA: 'letstalk_user',
  DRAFT_POST: 'letstalk_draft_post',
  SETTINGS: 'letstalk_settings'
} as const

// Theme modes
export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system'
} as const

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    ME: '/api/auth/me',
    REFRESH: '/api/auth/refresh-token'
  },
  POSTS: {
    BASE: '/api/posts',
    CREATE: '/api/posts',
    LIKE: '/api/likes',
    SAVE: '/api/saves'
  },
  USERS: {
    BASE: '/api/users',
    FOLLOW: '/api/follows',
    SUGGESTED: '/api/users/suggested'
  },
  MESSAGES: {
    BASE: '/api/messages',
    CONVERSATIONS: '/api/conversations'
  },
  NOTIFICATIONS: {
    BASE: '/api/notifications',
    MARK_READ: '/api/notifications/mark-read',
    SUBSCRIBE: '/api/notifications/subscribe'
  }
} as const

// File upload constraints
export const FILE_CONSTRAINTS = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: {
    IMAGES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    VIDEOS: ['video/mp4', 'video/webm', 'video/ogg'],
    AUDIO: ['audio/mp3', 'audio/wav', 'audio/ogg']
  }
} as const

// Pagination defaults
export const PAGINATION = {
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 50
} as const

// Socket events
export const SOCKET_EVENTS = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  MESSAGE: 'message',
  NOTIFICATION: 'notification',
  USER_ONLINE: 'user_online',
  USER_OFFLINE: 'user_offline',
  TYPING: 'typing',
  STOP_TYPING: 'stop_typing'
} as const

// Error messages
export const ERROR_MESSAGES = {
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION: 'Please check your input and try again.',
  FILE_TOO_LARGE: 'File size is too large. Maximum size is 10MB.',
  INVALID_FILE_TYPE: 'Invalid file type. Please select a valid file.'
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  POST_CREATED: 'Post created successfully!',
  POST_UPDATED: 'Post updated successfully!',
  POST_DELETED: 'Post deleted successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  SETTINGS_SAVED: 'Settings saved successfully!'
} as const

// App configuration
export const APP_CONFIG = {
  NAME: "Let's Talk",
  VERSION: '1.0.0',
  DESCRIPTION: 'A modern social media platform',
  SUPPORT_EMAIL: '<EMAIL>',
  PRIVACY_URL: '/privacy',
  TERMS_URL: '/terms'
} as const

export default {
  STORAGE_KEYS,
  THEME_MODES,
  API_ENDPOINTS,
  FILE_CONSTRAINTS,
  PAGINATION,
  SOCKET_EVENTS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  APP_CONFIG
}
