import { createContext, useContext, useEffect, useState } from 'react'
import { io } from 'socket.io-client'
import { useAuth } from './AuthContext'

const SocketContext = createContext()

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null)
  const [onlineUsers, setOnlineUsers] = useState([])
  const { user, isAuthenticated } = useAuth()

  useEffect(() => {
    if (isAuthenticated && user) {
      // Get socket URL from dynamic config or fallback
      const socketUrl = window.dynamicConfig?.socketUrl || 'http://localhost:10001'

      const newSocket = io(socketUrl, {
        auth: {
          token: localStorage.getItem('token')
        },
        transports: ['websocket', 'polling']
      })

      newSocket.on('connect', () => {
        console.log('Connected to socket server')
        setSocket(newSocket)
      })

      newSocket.on('disconnect', () => {
        console.log('Disconnected from socket server')
      })

      newSocket.on('connect_error', (error) => {
        console.error('Socket connection error:', error)
      })

      newSocket.on('getOnlineUsers', (users) => {
        setOnlineUsers(users)
      })

      return () => {
        newSocket.close()
        setSocket(null)
      }
    } else {
      if (socket) {
        socket.close()
        setSocket(null)
      }
    }
  }, [isAuthenticated, user?.id]) // Only depend on user ID, not the entire user object

  const value = {
    socket,
    onlineUsers,
    isConnected: socket?.connected || false
  }

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  )
}

export const useSocket = () => {
  const context = useContext(SocketContext)
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider')
  }
  return context
}

export default SocketContext
