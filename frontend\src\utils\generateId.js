/**
 * Generate a unique ID
 * @param {number} length - Length of the ID (default: 8)
 * @param {string} prefix - Optional prefix for the ID
 * @returns {string} Generated unique ID
 */
const generateId = (length = 8, prefix = '') => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = prefix
  
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length))
  }
  
  return result
}

/**
 * Generate a UUID v4
 * @returns {string} UUID v4 string
 */
export const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * Generate a short ID (nanoid style)
 * @param {number} size - Size of the ID (default: 10)
 * @returns {string} Short ID
 */
export const generateShortId = (size = 10) => {
  const alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'
  let id = ''
  
  for (let i = 0; i < size; i++) {
    id += alphabet[Math.floor(Math.random() * alphabet.length)]
  }
  
  return id
}

/**
 * Generate a timestamp-based ID
 * @param {string} prefix - Optional prefix
 * @returns {string} Timestamp-based ID
 */
export const generateTimestampId = (prefix = '') => {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substr(2, 5)
  return `${prefix}${timestamp}${random}`
}

/**
 * Generate a numeric ID
 * @param {number} length - Length of the numeric ID (default: 6)
 * @returns {string} Numeric ID
 */
export const generateNumericId = (length = 6) => {
  let result = ''
  
  for (let i = 0; i < length; i++) {
    result += Math.floor(Math.random() * 10).toString()
  }
  
  return result
}

export default generateId
