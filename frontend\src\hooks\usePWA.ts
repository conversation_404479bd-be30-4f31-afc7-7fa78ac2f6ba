import { useState, useEffect } from 'react'
import { useSnackbar } from 'notistack'

interface PWAState {
  isInstallable: boolean
  isInstalled: boolean
  isOnline: boolean
  isUpdateAvailable: boolean
  registration: ServiceWorkerRegistration | null
}

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

export const usePWA = () => {
  const [state, setState] = useState<PWAState>({
    isInstallable: false,
    isInstalled: false,
    isOnline: navigator.onLine,
    isUpdateAvailable: false,
    registration: null,
  })
  
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const { enqueueSnackbar } = useSnackbar()

  useEffect(() => {
    // Check if app is already installed
    const checkInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches
      const isInWebAppiOS = (window.navigator as any).standalone === true
      setState(prev => ({ ...prev, isInstalled: isStandalone || isInWebAppiOS }))
    }

    checkInstalled()

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      const event = e as BeforeInstallPromptEvent
      setDeferredPrompt(event)
      setState(prev => ({ ...prev, isInstallable: true }))
    }

    // Listen for app installed event
    const handleAppInstalled = () => {
      setState(prev => ({ ...prev, isInstalled: true, isInstallable: false }))
      setDeferredPrompt(null)
      enqueueSnackbar('App installed successfully!', { variant: 'success' })
    }

    // Listen for online/offline events
    const handleOnline = () => {
      setState(prev => ({ ...prev, isOnline: true }))
      enqueueSnackbar('You are back online!', { variant: 'success' })
    }

    const handleOffline = () => {
      setState(prev => ({ ...prev, isOnline: false }))
      enqueueSnackbar('You are offline. Some features may be limited.', { 
        variant: 'warning',
        persist: true 
      })
    }

    // Register service worker
    const registerServiceWorker = async () => {
      if ('serviceWorker' in navigator) {
        try {
          const registration = await navigator.serviceWorker.register('/sw.js')
          setState(prev => ({ ...prev, registration }))

          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setState(prev => ({ ...prev, isUpdateAvailable: true }))
                  enqueueSnackbar('New version available!', {
                    variant: 'info',
                    persist: true,
                    action: (key) => (
                      <button
                        onClick={() => {
                          newWorker.postMessage({ type: 'SKIP_WAITING' })
                          window.location.reload()
                        }}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: 'white',
                          textDecoration: 'underline',
                          cursor: 'pointer'
                        }}
                      >
                        Update
                      </button>
                    )
                  })
                }
              })
            }
          })

          console.log('Service Worker registered successfully')
        } catch (error) {
          console.error('Service Worker registration failed:', error)
        }
      }
    }

    // Add event listeners
    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Register service worker
    registerServiceWorker()

    // Cleanup
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [enqueueSnackbar])

  const installApp = async () => {
    if (!deferredPrompt) return false

    try {
      await deferredPrompt.prompt()
      const choiceResult = await deferredPrompt.userChoice
      
      if (choiceResult.outcome === 'accepted') {
        console.log('User accepted the install prompt')
        return true
      } else {
        console.log('User dismissed the install prompt')
        return false
      }
    } catch (error) {
      console.error('Error during app installation:', error)
      return false
    } finally {
      setDeferredPrompt(null)
      setState(prev => ({ ...prev, isInstallable: false }))
    }
  }

  const updateApp = () => {
    if (state.registration && state.registration.waiting) {
      state.registration.waiting.postMessage({ type: 'SKIP_WAITING' })
      window.location.reload()
    }
  }

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      return permission === 'granted'
    }
    return false
  }

  const subscribeToPushNotifications = async () => {
    if (!state.registration) return null

    try {
      const subscription = await state.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: process.env.VITE_VAPID_PUBLIC_KEY
      })
      
      // Send subscription to server
      // await fetch('/api/notifications/subscribe', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(subscription)
      // })

      return subscription
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error)
      return null
    }
  }

  const shareContent = async (data: ShareData) => {
    if (navigator.share) {
      try {
        await navigator.share(data)
        return true
      } catch (error) {
        console.error('Error sharing:', error)
        return false
      }
    } else {
      // Fallback to clipboard
      if (navigator.clipboard && data.url) {
        try {
          await navigator.clipboard.writeText(data.url)
          enqueueSnackbar('Link copied to clipboard!', { variant: 'success' })
          return true
        } catch (error) {
          console.error('Error copying to clipboard:', error)
          return false
        }
      }
      return false
    }
  }

  return {
    ...state,
    installApp,
    updateApp,
    requestNotificationPermission,
    subscribeToPushNotifications,
    shareContent,
    canInstall: state.isInstallable && !state.isInstalled,
    canShare: 'share' in navigator,
    canNotify: 'Notification' in window,
  }
}

export default usePWA
