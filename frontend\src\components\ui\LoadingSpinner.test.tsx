import { render, screen } from '@/test/utils'
import LoadingSpinner from './LoadingSpinner'

describe('LoadingSpinner', () => {
  it('renders with default props', () => {
    render(<LoadingSpinner />)
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
    expect(screen.getByText('Loading...')).toBeInTheDocument()
  })

  it('renders with custom message', () => {
    const customMessage = 'Please wait...'
    render(<LoadingSpinner message={customMessage} />)
    
    expect(screen.getByText(customMessage)).toBeInTheDocument()
  })

  it('renders without message when not provided', () => {
    render(<LoadingSpinner message="" />)
    
    expect(screen.getByRole('progressbar')).toBeInTheDocument()
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument()
  })

  it('renders in fullscreen mode', () => {
    render(<LoadingSpinner fullScreen />)
    
    const container = screen.getByRole('progressbar').closest('div')
    expect(container).toHaveStyle({
      position: 'fixed',
      top: '0',
      left: '0',
      right: '0',
      bottom: '0',
    })
  })

  it('applies custom size', () => {
    render(<LoadingSpinner size={60} />)
    
    const progressbar = screen.getByRole('progressbar')
    expect(progressbar).toHaveStyle({
      width: '60px',
      height: '60px',
    })
  })

  it('applies different colors', () => {
    const { rerender } = render(<LoadingSpinner color="primary" />)
    
    let progressbar = screen.getByRole('progressbar')
    expect(progressbar).toHaveClass('MuiCircularProgress-colorPrimary')

    rerender(<LoadingSpinner color="secondary" />)
    progressbar = screen.getByRole('progressbar')
    expect(progressbar).toHaveClass('MuiCircularProgress-colorSecondary')
  })

  it('has proper accessibility attributes', () => {
    render(<LoadingSpinner message="Loading content" />)
    
    const progressbar = screen.getByRole('progressbar')
    expect(progressbar).toBeInTheDocument()
    
    const message = screen.getByText('Loading content')
    expect(message).toBeInTheDocument()
  })
})
