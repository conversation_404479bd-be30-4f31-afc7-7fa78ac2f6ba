import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Avatar,
  Button,
  Divider,
  Badge,
  useTheme
} from '@mui/material'
import {
  Home,
  Person,
  Message,
  PhotoLibrary,
  Add,
  Settings,
  Logout,
  Notifications,
  TrendingUp,
  Store,
  LiveTv
} from '@mui/icons-material'
import { useAuth } from '../../context/AuthContext'
import { useNotifications } from '../../hooks'

const Sidebar = ({ open, onClose, variant = 'permanent', width = 280 }) => {
  const { user, logout } = useAuth()
  const location = useLocation()
  const theme = useTheme()
  const { unreadCount } = useNotifications()

  const navigation = [
    { 
      name: 'Home', 
      href: '/', 
      icon: Home,
      description: 'Your personalized feed'
    },
    { 
      name: 'Messages', 
      href: '/messages', 
      icon: Message,
      description: 'Chat with friends',
      badge: 3 // Example unread count
    },
    { 
      name: 'Gallery', 
      href: '/gallery', 
      icon: PhotoLibrary,
      description: 'Photos and videos'
    },
    { 
      name: 'Live', 
      href: '/live', 
      icon: LiveTv,
      description: 'Live streams'
    },
    { 
      name: 'Shop', 
      href: '/shop', 
      icon: Store,
      description: 'Marketplace'
    },
    { 
      name: 'Trending', 
      href: '/trending', 
      icon: TrendingUp,
      description: 'What\'s popular'
    },
  ]

  const secondaryNavigation = [
    { 
      name: 'Profile', 
      href: `/profile/${user?.id}`, 
      icon: Person,
      description: 'Your profile'
    },
    { 
      name: 'Notifications', 
      href: '/notifications', 
      icon: Notifications,
      description: 'Your notifications',
      badge: unreadCount
    },
    { 
      name: 'Settings', 
      href: '/settings', 
      icon: Settings,
      description: 'App settings'
    },
  ]

  const isActive = (path) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  const renderNavItem = (item) => {
    const Icon = item.icon
    const active = isActive(item.href)
    
    return (
      <ListItem key={item.name} disablePadding>
        <ListItemButton
          component={Link}
          to={item.href}
          selected={active}
          onClick={variant === 'temporary' ? onClose : undefined}
          sx={{
            borderRadius: 2,
            mb: 0.5,
            mx: 1,
            '&.Mui-selected': {
              backgroundColor: 'primary.main',
              color: 'primary.contrastText',
              '&:hover': {
                backgroundColor: 'primary.dark',
              },
              '& .MuiListItemIcon-root': {
                color: 'primary.contrastText',
              },
            },
            '&:hover': {
              backgroundColor: active ? 'primary.dark' : 'action.hover',
            },
          }}
        >
          <ListItemIcon sx={{ minWidth: 40 }}>
            {item.badge ? (
              <Badge badgeContent={item.badge} color="error">
                <Icon />
              </Badge>
            ) : (
              <Icon />
            )}
          </ListItemIcon>
          <ListItemText 
            primary={item.name}
            secondary={item.description}
            primaryTypographyProps={{
              fontWeight: active ? 600 : 400,
              fontSize: '0.95rem'
            }}
            secondaryTypographyProps={{
              fontSize: '0.75rem',
              color: active ? 'primary.contrastText' : 'text.secondary'
            }}
          />
        </ListItemButton>
      </ListItem>
    )
  }

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo */}
      <Box sx={{ 
        p: 3, 
        textAlign: 'center', 
        borderBottom: 1, 
        borderColor: 'divider',
        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
      }}>
        <Typography 
          variant="h4" 
          component="h1" 
          sx={{
            fontWeight: 800,
            background: 'linear-gradient(45deg, #fff, #f0f0f0)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            textShadow: '0 2px 4px rgba(0,0,0,0.3)'
          }}
        >
          Let's Talk
        </Typography>
        <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.8)' }}>
          Connect • Share • Inspire
        </Typography>
      </Box>

      {/* User Profile */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box display="flex" alignItems="center" gap={2}>
          <Avatar 
            src={user?.avatar}
            sx={{ 
              width: 48, 
              height: 48,
              border: 2,
              borderColor: 'primary.main'
            }}
          >
            {user?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <Box sx={{ flexGrow: 1, minWidth: 0 }}>
            <Typography variant="subtitle1" fontWeight={600} noWrap>
              {user?.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" noWrap>
              @{user?.username}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {user?.followersCount || 0} followers
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Primary Navigation */}
      <List sx={{ flexGrow: 1, py: 1 }}>
        {navigation.map(renderNavItem)}
        
        <Divider sx={{ my: 2, mx: 2 }} />
        
        {secondaryNavigation.map(renderNavItem)}
      </List>

      {/* Create Post Button */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Button
          component={Link}
          to="/create"
          fullWidth
          variant="contained"
          size="large"
          startIcon={<Add />}
          sx={{
            borderRadius: 3,
            py: 1.5,
            fontWeight: 600,
            background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            '&:hover': {
              background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
            }
          }}
        >
          Create Post
        </Button>
      </Box>

      {/* Logout */}
      <Box sx={{ p: 2 }}>
        <Button
          fullWidth
          startIcon={<Logout />}
          onClick={logout}
          color="error"
          variant="outlined"
          sx={{ borderRadius: 2 }}
        >
          Logout
        </Button>
      </Box>
    </Box>
  )

  return (
    <Drawer
      variant={variant}
      open={open}
      onClose={onClose}
      sx={{
        width: width,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: width,
          boxSizing: 'border-box',
          borderRight: 1,
          borderColor: 'divider',
        },
      }}
    >
      {drawerContent}
    </Drawer>
  )
}

export default Sidebar
