import { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { ThemeProvider } from '@mui/material/styles'
import { CssBaseline } from '@mui/material'
import { SnackbarProvider } from 'notistack'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { AuthProvider } from '@/context/AuthContext'
import { SocketProvider } from '@/context/SocketContext'
import { lightTheme } from '@/styles/theme'
import type { User } from '@/types'

// Mock user for testing
export const mockUser: User = {
  id: '1',
  username: 'testuser',
  name: 'Test User',
  email: '<EMAIL>',
  avatar: 'https://example.com/avatar.jpg',
  verified: false,
  followersCount: 100,
  followingCount: 50,
  postsCount: 25,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
}

// Create a test query client
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: Infinity,
      },
      mutations: {
        retry: false,
      },
    },
  })

interface AllTheProvidersProps {
  children: React.ReactNode
  queryClient?: QueryClient
  user?: User | null
  authenticated?: boolean
}

const AllTheProviders = ({ 
  children, 
  queryClient = createTestQueryClient(),
  user = mockUser,
  authenticated = true 
}: AllTheProvidersProps) => {
  // Mock auth context value
  const mockAuthValue = {
    user: authenticated ? user : null,
    token: authenticated ? 'mock-token' : null,
    loading: false,
    error: null,
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    updateUser: jest.fn(),
    clearErrors: jest.fn(),
    loadUser: jest.fn(),
  }

  // Mock socket context value
  const mockSocketValue = {
    socket: null,
    connected: false,
    onlineUsers: [],
    joinRoom: jest.fn(),
    leaveRoom: jest.fn(),
    sendMessage: jest.fn(),
  }

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ThemeProvider theme={lightTheme}>
          <CssBaseline />
          <SnackbarProvider maxSnack={3}>
            <AuthProvider value={mockAuthValue}>
              <SocketProvider value={mockSocketValue}>
                {children}
              </SocketProvider>
            </AuthProvider>
          </SnackbarProvider>
        </ThemeProvider>
      </BrowserRouter>
    </QueryClientProvider>
  )
}

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient
  user?: User | null
  authenticated?: boolean
}

const customRender = (
  ui: ReactElement,
  {
    queryClient,
    user,
    authenticated,
    ...renderOptions
  }: CustomRenderOptions = {}
) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <AllTheProviders
      queryClient={queryClient}
      user={user}
      authenticated={authenticated}
    >
      {children}
    </AllTheProviders>
  )

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Helper to render with unauthenticated state
export const renderUnauthenticated = (ui: ReactElement, options?: CustomRenderOptions) =>
  customRender(ui, { ...options, authenticated: false, user: null })

// Helper to render with custom user
export const renderWithUser = (ui: ReactElement, user: User, options?: CustomRenderOptions) =>
  customRender(ui, { ...options, user, authenticated: true })

// Helper to create mock functions
export const createMockFn = <T extends (...args: any[]) => any>(
  implementation?: T
): jest.MockedFunction<T> => {
  return jest.fn(implementation) as jest.MockedFunction<T>
}

// Helper to wait for loading states
export const waitForLoadingToFinish = () =>
  new Promise(resolve => setTimeout(resolve, 0))

// Helper to create mock post
export const createMockPost = (overrides = {}) => ({
  id: '1',
  content: 'Test post content',
  media: [],
  author: mockUser,
  likesCount: 0,
  commentsCount: 0,
  sharesCount: 0,
  isLiked: false,
  isSaved: false,
  privacy: 'public' as const,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  ...overrides,
})

// Helper to create mock comment
export const createMockComment = (overrides = {}) => ({
  id: '1',
  content: 'Test comment',
  author: mockUser,
  postId: '1',
  likesCount: 0,
  isLiked: false,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  ...overrides,
})

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }
