const Comment = require('../models/Comment');
const Post = require('../models/Post');
const Reel = require('../models/Reel');
const User = require('../models/User');
const { createError } = require('../utils/error');
const notificationService = require('../services/notificationService');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Create a new comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createComment = async (req, res, next) => {
  try {
    const { post, reel, text, parent } = req.body;

    // Check if at least one item is provided
    if (!post && !reel) {
      return next(createError(400, 'Please provide a post or reel to comment on'));
    }

    // Check if text is provided
    if (!text) {
      return next(createError(400, 'Please provide comment text'));
    }

    // Create comment
    const comment = await Comment.create({
      user: req.user.id,
      text,
      ...(post && { post }),
      ...(reel && { reel }),
      ...(parent && { parentComment: parent }),
    });

    // Populate user details
    await comment.populate('user', 'username name profilePicture');

    // Update comment count
    let itemType, itemId, ownerId, mediaUrl, mediaType, mediaThumbnail;

    if (post) {
      const postDoc = await Post.findById(post);
      if (!postDoc) {
        return next(createError(404, 'Post not found'));
      }

      postDoc.commentsCount = (postDoc.commentsCount || 0) + 1;
      await postDoc.save();

      itemType = 'post';
      itemId = post;
      ownerId = postDoc.user.toString();

      // Get media info for notification
      if (postDoc.media && postDoc.media.length > 0) {
        mediaUrl = postDoc.media[0].url;
        mediaType = postDoc.media[0].type;
        if (mediaType === 'video' && postDoc.media[0].thumbnail) {
          mediaThumbnail = postDoc.media[0].thumbnail;
        }
      }
    } else if (reel) {
      const reelDoc = await Reel.findById(reel);
      if (!reelDoc) {
        return next(createError(404, 'Reel not found'));
      }

      reelDoc.commentsCount = (reelDoc.commentsCount || 0) + 1;
      await reelDoc.save();

      itemType = 'reel';
      itemId = reel;
      ownerId = reelDoc.user.toString();

      // Get media info for notification
      mediaUrl = reelDoc.video.url;
      mediaType = 'video';
      if (reelDoc.thumbnail) {
        mediaThumbnail = reelDoc.thumbnail.url;
      }
    }

    // If this is a reply to another comment, update the parent comment's reply count
    if (parent) {
      const parentCommentDoc = await Comment.findById(parent);
      if (parentCommentDoc) {
        parentCommentDoc.repliesCount = (parentCommentDoc.repliesCount || 0) + 1;
        await parentCommentDoc.save();

        // Create notification for the parent comment owner if different from commenter
        if (parentCommentDoc.user.toString() !== req.user.id) {
          const replyNotification = await Notification.create({
            recipient: parentCommentDoc.user,
            type: 'comment',
            sender: req.user.id,
            comment: comment._id,
            text: text.substring(0, 100),
            ...(post && { post }),
            ...(reel && { reel }),
            mediaUrl,
            mediaType,
            mediaThumbnail,
          });

          // Populate sender details for real-time notification
          await replyNotification.populate('sender', 'username name profilePicture');

          // Emit notification via Socket.IO
          socketEmitter.emitNotification(parentCommentDoc.user.toString(), replyNotification);
        }
      }
    }

    // Create notification if the owner is not the same as the commenter
    if (ownerId && ownerId !== req.user.id) {
      // Get sender details
      const sender = await User.findById(req.user.id).select('username name profilePicture');

      // Determine notification type and message
      let notificationType, title, message;

      if (post) {
        notificationType = 'comment_post';
        title = 'New Comment';
        message = `${sender.name || sender.username} commented on your post`;
      } else if (reel) {
        notificationType = 'comment_reel';
        title = 'New Comment';
        message = `${sender.name || sender.username} commented on your reel`;
      }

      // Send notification using notification service
      await notificationService.sendNotification(ownerId, {
        type: notificationType,
        title,
        message,
        data: {
          senderId: req.user.id,
          senderName: sender.name || sender.username,
          senderProfilePicture: sender.profilePicture,
          commentId: comment._id,
          commentText: text.substring(0, 100),
          ...(post && { postId: post }),
          ...(reel && { reelId: reel }),
          mediaUrl,
          mediaType,
          mediaThumbnail
        },
        priority: 'normal',
        actionUrl: post ? `/posts/${post}` : `/reels/${reel}`,
        actionText: 'View'
      });
    }

    // Check for mentions in the comment text
    const mentionRegex = /@(\w+)/g;
    const mentions = text.match(mentionRegex);

    if (mentions) {
      const uniqueMentions = [...new Set(mentions.map(m => m.substring(1)))];

      // Find mentioned users
      const mentionedUsers = await User.find({
        username: { $in: uniqueMentions },
      });

      // Create notifications for mentioned users
      for (const mentionedUser of mentionedUsers) {
        // Skip if mentioned user is the commenter or the post/reel owner
        if (
          mentionedUser._id.toString() === req.user.id ||
          mentionedUser._id.toString() === ownerId
        ) {
          continue;
        }

        const mentionNotification = await Notification.create({
          recipient: mentionedUser._id,
          type: 'mention',
          sender: req.user.id,
          comment: comment._id,
          text: text.substring(0, 100),
          ...(post && { post }),
          ...(reel && { reel }),
          mediaUrl,
          mediaType,
          mediaThumbnail,
        });

        // Populate sender details for real-time notification
        await mentionNotification.populate('sender', 'username name profilePicture');

        // Emit notification via Socket.IO
        socketEmitter.emitNotification(mentionedUser._id.toString(), mentionNotification);
      }
    }

    // Emit comment event via Socket.IO
    socketEmitter.emitComment(req.user.id, itemType, itemId, comment._id, ownerId);

    res.status(201).json({
      success: true,
      data: comment,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get comments for a post or reel
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getComments = async (req, res, next) => {
  try {
    const { type, id } = req.params;
    const { parent, limit = 20, page = 1 } = req.query;

    // Validate type
    if (!['post', 'reel'].includes(type)) {
      return next(createError(400, 'Invalid type. Must be post or reel'));
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build query
    const query = {
      [type]: id,
      ...(parent ? { parentComment: parent } : { parentComment: { $exists: false } }),
    };

    // Find comments
    const comments = await Comment.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture')
      .populate({
        path: 'parentComment',
        select: 'user text',
        populate: {
          path: 'user',
          select: 'username',
        },
      });

    // Get total count
    const total = await Comment.countDocuments(query);

    // Check if user has liked each comment
    const commentsWithLikeStatus = await Promise.all(
      comments.map(async (comment) => {
        const commentObj = comment.toObject();

        if (req.user) {
          const Like = require('../models/Like');
          const isLiked = await Like.exists({
            user: req.user.id,
            comment: comment._id,
          });

          commentObj.isLiked = !!isLiked;
        } else {
          commentObj.isLiked = false;
        }

        return commentObj;
      })
    );

    res.status(200).json({
      success: true,
      count: comments.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: commentsWithLikeStatus,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Update a comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updateComment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { text } = req.body;

    // Find comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Check if user is the owner of the comment
    if (comment.user.toString() !== req.user.id) {
      return next(createError(403, 'You are not authorized to update this comment'));
    }

    // Update comment
    comment.text = text;
    comment.isEdited = true;
    await comment.save();

    res.status(200).json({
      success: true,
      data: comment,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteComment = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Check if user is the owner of the comment or the post/reel
    let isAuthorized = comment.user.toString() === req.user.id;

    if (!isAuthorized) {
      // Check if user is the owner of the post/reel
      if (comment.post) {
        const post = await Post.findById(comment.post);
        isAuthorized = post && post.user.toString() === req.user.id;
      } else if (comment.reel) {
        const reel = await Reel.findById(comment.reel);
        isAuthorized = reel && reel.user.toString() === req.user.id;
      }
    }

    if (!isAuthorized) {
      return next(createError(403, 'You are not authorized to delete this comment'));
    }

    // Delete comment
    await comment.deleteOne();

    // Update comment count
    if (comment.post) {
      const post = await Post.findById(comment.post);
      if (post) {
        post.commentsCount = Math.max(0, (post.commentsCount || 0) - 1);
        await post.save();
      }
    } else if (comment.reel) {
      const reel = await Reel.findById(comment.reel);
      if (reel) {
        reel.commentsCount = Math.max(0, (reel.commentsCount || 0) - 1);
        await reel.save();
      }
    }

    // If this is a reply, update the parent comment's reply count
    if (comment.parentComment) {
      const parentComment = await Comment.findById(comment.parentComment);
      if (parentComment) {
        parentComment.repliesCount = Math.max(0, (parentComment.repliesCount || 0) - 1);
        await parentComment.save();
      }
    }

    res.status(200).json({
      success: true,
      message: 'Comment deleted successfully',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get a single comment by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getComment = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find comment
    const comment = await Comment.findById(id)
      .populate('user', 'username name profilePicture')
      .populate({
        path: 'parentComment',
        select: 'user text',
        populate: {
          path: 'user',
          select: 'username',
        },
      });

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Check if user has liked the comment
    let isLiked = false;
    if (req.user) {
      const Like = require('../models/Like');
      const like = await Like.findOne({
        user: req.user.id,
        comment: comment._id,
      });
      isLiked = !!like;
    }

    const commentObj = comment.toObject();
    commentObj.isLiked = isLiked;

    res.status(200).json({
      success: true,
      data: commentObj,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Like a comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.likeComment = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Check if user has already liked the comment
    const Like = require('../models/Like');
    const existingLike = await Like.findOne({
      user: req.user.id,
      comment: comment._id,
    });

    if (existingLike) {
      return next(createError(400, 'You have already liked this comment'));
    }

    // Create like
    const like = await Like.create({
      user: req.user.id,
      comment: comment._id,
    });

    // Update like count
    comment.likesCount = (comment.likesCount || 0) + 1;
    await comment.save();

    // Create notification if the comment owner is not the same as the liker
    if (comment.user.toString() !== req.user.id) {
      const Notification = require('../models/Notification');

      // Get post or reel info for notification
      let post, reel, mediaUrl, mediaType, mediaThumbnail;

      if (comment.post) {
        post = comment.post;
        const postDoc = await Post.findById(post);
        if (postDoc && postDoc.media && postDoc.media.length > 0) {
          mediaUrl = postDoc.media[0].url;
          mediaType = postDoc.media[0].type;
          if (mediaType === 'video' && postDoc.media[0].thumbnail) {
            mediaThumbnail = postDoc.media[0].thumbnail;
          }
        }
      } else if (comment.reel) {
        reel = comment.reel;
        const reelDoc = await Reel.findById(reel);
        if (reelDoc) {
          mediaUrl = reelDoc.video.url;
          mediaType = 'video';
          if (reelDoc.thumbnail) {
            mediaThumbnail = reelDoc.thumbnail.url;
          }
        }
      }

      const notification = await Notification.create({
        recipient: comment.user,
        type: 'like',
        sender: req.user.id,
        comment: comment._id,
        text: comment.text.substring(0, 100),
        ...(post && { post }),
        ...(reel && { reel }),
        mediaUrl,
        mediaType,
        mediaThumbnail,
      });

      // Populate sender details for real-time notification
      await notification.populate('sender', 'username name profilePicture');

      // Emit notification via Socket.IO
      socketEmitter.emitNotification(comment.user.toString(), notification);
    }

    res.status(200).json({
      success: true,
      data: like,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Unlike a comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.unlikeComment = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Check if user has liked the comment
    const Like = require('../models/Like');
    const like = await Like.findOne({
      user: req.user.id,
      comment: comment._id,
    });

    if (!like) {
      return next(createError(400, 'You have not liked this comment'));
    }

    // Delete like
    await like.deleteOne();

    // Update like count
    comment.likesCount = Math.max(0, (comment.likesCount || 0) - 1);
    await comment.save();

    res.status(200).json({
      success: true,
      message: 'Comment unliked successfully',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get likes for a comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getCommentLikes = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { limit = 20, page = 1 } = req.query;

    // Find comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Find likes
    const Like = require('../models/Like');
    const likes = await Like.find({ comment: id })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture');

    // Get total count
    const total = await Like.countDocuments({ comment: id });

    res.status(200).json({
      success: true,
      count: likes.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: likes,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get replies for a comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getCommentReplies = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { limit = 20, page = 1 } = req.query;

    // Find comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Find replies
    const replies = await Comment.find({ parentComment: id })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture');

    // Get total count
    const total = await Comment.countDocuments({ parentComment: id });

    // Check if user has liked each reply
    const repliesWithLikeStatus = await Promise.all(
      replies.map(async (reply) => {
        const replyObj = reply.toObject();

        if (req.user) {
          const Like = require('../models/Like');
          const isLiked = await Like.exists({
            user: req.user.id,
            comment: reply._id,
          });

          replyObj.isLiked = !!isLiked;
        } else {
          replyObj.isLiked = false;
        }

        return replyObj;
      })
    );

    res.status(200).json({
      success: true,
      count: replies.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: repliesWithLikeStatus,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Create a reply to a comment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createCommentReply = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { text } = req.body;

    // Find parent comment
    const parentComment = await Comment.findById(id);

    if (!parentComment) {
      return next(createError(404, 'Parent comment not found'));
    }

    // Check if text is provided
    if (!text) {
      return next(createError(400, 'Please provide reply text'));
    }

    // Create reply
    const reply = await Comment.create({
      user: req.user.id,
      text,
      parentComment: id,
      ...(parentComment.post && { post: parentComment.post }),
      ...(parentComment.reel && { reel: parentComment.reel }),
    });

    // Populate user details
    await reply.populate('user', 'username name profilePicture');

    // Update parent comment's reply count
    parentComment.repliesCount = (parentComment.repliesCount || 0) + 1;
    await parentComment.save();

    // Create notification for the parent comment owner if different from replier
    if (parentComment.user.toString() !== req.user.id) {
      const Notification = require('../models/Notification');

      // Get post or reel info for notification
      let post, reel, mediaUrl, mediaType, mediaThumbnail;

      if (parentComment.post) {
        post = parentComment.post;
        const postDoc = await Post.findById(post);
        if (postDoc && postDoc.media && postDoc.media.length > 0) {
          mediaUrl = postDoc.media[0].url;
          mediaType = postDoc.media[0].type;
          if (mediaType === 'video' && postDoc.media[0].thumbnail) {
            mediaThumbnail = postDoc.media[0].thumbnail;
          }
        }
      } else if (parentComment.reel) {
        reel = parentComment.reel;
        const reelDoc = await Reel.findById(reel);
        if (reelDoc) {
          mediaUrl = reelDoc.video.url;
          mediaType = 'video';
          if (reelDoc.thumbnail) {
            mediaThumbnail = reelDoc.thumbnail.url;
          }
        }
      }

      const notification = await Notification.create({
        recipient: parentComment.user,
        type: 'comment',
        sender: req.user.id,
        comment: reply._id,
        text: text.substring(0, 100),
        ...(post && { post }),
        ...(reel && { reel }),
        mediaUrl,
        mediaType,
        mediaThumbnail,
      });

      // Populate sender details for real-time notification
      await notification.populate('sender', 'username name profilePicture');

      // Emit notification via Socket.IO
      socketEmitter.emitNotification(parentComment.user.toString(), notification);
    }

    res.status(201).json({
      success: true,
      data: reply,
    });
  } catch (err) {
    next(err);
  }
};
