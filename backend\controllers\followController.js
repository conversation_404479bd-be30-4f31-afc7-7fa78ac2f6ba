const Follow = require('../models/Follow');
const User = require('../models/User');
const { createError } = require('../utils/error');
const notificationService = require('../services/notificationService');
const socketEmitter = require('../utils/socketEmitter');

/**
 * Follow a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.followUser = async (req, res, next) => {
  try {
    const { userId } = req.params;

    // Check if userId is provided
    if (!userId) {
      return next(createError(400, 'Please provide a user ID to follow'));
    }

    // Check if user is trying to follow themselves
    if (userId === req.user.id) {
      return next(createError(400, 'You cannot follow yourself'));
    }

    // Check if user exists
    const userToFollow = await User.findById(userId);
    if (!userToFollow) {
      return next(createError(404, 'User not found'));
    }

    // Check if already following
    const existingFollow = await Follow.findOne({
      follower: req.user.id,
      following: userId,
    });

    if (existingFollow) {
      return next(createError(400, 'You are already following this user'));
    }

    // Create follow
    const follow = await Follow.create({
      follower: req.user.id,
      following: userId,
    });

    // Update follower and following counts
    await User.findByIdAndUpdate(req.user.id, {
      $inc: { followingCount: 1 },
    });

    await User.findByIdAndUpdate(userId, {
      $inc: { followersCount: 1 },
    });

    // Get follower details for notification
    const follower = await User.findById(req.user.id).select('username name profilePicture');

    // Send follow notification
    await notificationService.sendNotification(userId, {
      type: 'follow',
      title: 'New Follower',
      message: `${follower.name || follower.username} started following you`,
      data: {
        senderId: req.user.id,
        senderName: follower.name || follower.username,
        senderUsername: follower.username,
        senderProfilePicture: follower.profilePicture,
        followId: follow._id
      },
      priority: 'normal',
      actionUrl: `/profile/${req.user.id}`,
      actionText: 'View Profile'
    });

    // Emit follow event via Socket.IO
    socketEmitter.emitFollow(req.user.id, userId);

    res.status(201).json({
      success: true,
      data: follow,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Unfollow a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.unfollowUser = async (req, res, next) => {
  try {
    const { userId } = req.params;

    // Check if user exists
    const userToUnfollow = await User.findById(userId);
    if (!userToUnfollow) {
      return next(createError(404, 'User not found'));
    }

    // Check if following
    const follow = await Follow.findOne({
      follower: req.user.id,
      following: userId,
    });

    if (!follow) {
      return next(createError(400, 'You are not following this user'));
    }

    // Delete follow
    await follow.deleteOne();

    // Update follower and following counts
    await User.findByIdAndUpdate(req.user.id, {
      $inc: { followingCount: -1 },
    });

    await User.findByIdAndUpdate(userId, {
      $inc: { followersCount: -1 },
    });

    // Emit unfollow event via Socket.IO
    if (socketEmitter) {
      socketEmitter.emit('unfollow', {
        sourceUserId: req.user.id,
        targetUserId: userId,
      });
    }

    res.status(200).json({
      success: true,
      message: 'User unfollowed successfully',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get followers of a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getFollowers = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { limit = 20, page = 1 } = req.query;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Find followers
    const followers = await Follow.find({ following: userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('follower', 'username name profilePicture');

    // Get total count
    const total = await Follow.countDocuments({ following: userId });

    // Check if authenticated user is following each follower
    const followersWithFollowStatus = await Promise.all(
      followers.map(async (follow) => {
        const followObj = follow.toObject();

        if (req.user) {
          const isFollowing = await Follow.exists({
            follower: req.user.id,
            following: follow.follower._id,
          });

          followObj.isFollowing = !!isFollowing;
        } else {
          followObj.isFollowing = false;
        }

        // Rename follower to user for consistency
        followObj.user = followObj.follower;
        delete followObj.follower;

        return followObj;
      })
    );

    res.status(200).json({
      success: true,
      count: followers.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: followersWithFollowStatus,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get users followed by a user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getFollowing = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { limit = 20, page = 1 } = req.query;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return next(createError(404, 'User not found'));
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Find following
    const following = await Follow.find({ follower: userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .populate('following', 'username name profilePicture');

    // Get total count
    const total = await Follow.countDocuments({ follower: userId });

    // Check if authenticated user is following each user
    const followingWithFollowStatus = await Promise.all(
      following.map(async (follow) => {
        const followObj = follow.toObject();

        if (req.user) {
          const isFollowing = await Follow.exists({
            follower: req.user.id,
            following: follow.following._id,
          });

          followObj.isFollowing = !!isFollowing;
        } else {
          followObj.isFollowing = false;
        }

        // Rename following to user for consistency
        followObj.user = followObj.following;
        delete followObj.following;

        return followObj;
      })
    );

    res.status(200).json({
      success: true,
      count: following.length,
      total,
      totalPages: Math.ceil(total / parseInt(limit)),
      currentPage: parseInt(page),
      data: followingWithFollowStatus,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Check if user is following another user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.checkFollow = async (req, res, next) => {
  try {
    const { userId } = req.params;

    // Check if user exists
    const userToCheck = await User.findById(userId);
    if (!userToCheck) {
      return next(createError(404, 'User not found'));
    }

    // Check if following
    const follow = await Follow.findOne({
      follower: req.user.id,
      following: userId,
    });

    res.status(200).json({
      success: true,
      isFollowing: !!follow,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Toggle notifications for a followed user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.toggleNotifications = async (req, res, next) => {
  try {
    const { userId } = req.params;
    const { enabled } = req.body;

    // Check if user exists
    const userToToggle = await User.findById(userId);
    if (!userToToggle) {
      return next(createError(404, 'User not found'));
    }

    // Check if following
    const follow = await Follow.findOne({
      follower: req.user.id,
      following: userId,
    });

    if (!follow) {
      return next(createError(400, 'You are not following this user'));
    }

    // Update notifications setting
    follow.notificationsEnabled = enabled;
    await follow.save();

    res.status(200).json({
      success: true,
      data: follow,
    });
  } catch (err) {
    next(err);
  }
};
