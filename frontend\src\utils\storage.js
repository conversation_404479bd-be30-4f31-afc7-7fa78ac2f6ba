/**
 * Enhanced localStorage wrapper with error handling and JSON support
 */
const storage = {
  /**
   * Get item from localStorage
   * @param {string} key - Storage key
   * @param {*} defaultValue - Default value if key doesn't exist
   * @returns {*} Stored value or default value
   */
  get(key, defaultValue = null) {
    try {
      const item = localStorage.getItem(key)
      if (item === null) return defaultValue
      
      // Try to parse as JSON, fallback to string
      try {
        return JSON.parse(item)
      } catch {
        return item
      }
    } catch (error) {
      console.warn(`Error getting item from localStorage: ${key}`, error)
      return defaultValue
    }
  },

  /**
   * Set item in localStorage
   * @param {string} key - Storage key
   * @param {*} value - Value to store
   * @returns {boolean} Success status
   */
  set(key, value) {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value)
      localStorage.setItem(key, serializedValue)
      return true
    } catch (error) {
      console.warn(`Error setting item in localStorage: ${key}`, error)
      return false
    }
  },

  /**
   * Remove item from localStorage
   * @param {string} key - Storage key
   * @returns {boolean} Success status
   */
  remove(key) {
    try {
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.warn(`Error removing item from localStorage: ${key}`, error)
      return false
    }
  },

  /**
   * Clear all localStorage
   * @returns {boolean} Success status
   */
  clear() {
    try {
      localStorage.clear()
      return true
    } catch (error) {
      console.warn('Error clearing localStorage', error)
      return false
    }
  },

  /**
   * Check if key exists in localStorage
   * @param {string} key - Storage key
   * @returns {boolean} Existence status
   */
  has(key) {
    try {
      return localStorage.getItem(key) !== null
    } catch (error) {
      console.warn(`Error checking localStorage key: ${key}`, error)
      return false
    }
  },

  /**
   * Get all keys from localStorage
   * @returns {string[]} Array of keys
   */
  keys() {
    try {
      return Object.keys(localStorage)
    } catch (error) {
      console.warn('Error getting localStorage keys', error)
      return []
    }
  },

  /**
   * Get localStorage size in bytes (approximate)
   * @returns {number} Size in bytes
   */
  size() {
    try {
      let total = 0
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          total += localStorage[key].length + key.length
        }
      }
      return total
    } catch (error) {
      console.warn('Error calculating localStorage size', error)
      return 0
    }
  }
}

/**
 * SessionStorage wrapper with same interface as localStorage wrapper
 */
export const sessionStorage = {
  get(key, defaultValue = null) {
    try {
      const item = window.sessionStorage.getItem(key)
      if (item === null) return defaultValue
      
      try {
        return JSON.parse(item)
      } catch {
        return item
      }
    } catch (error) {
      console.warn(`Error getting item from sessionStorage: ${key}`, error)
      return defaultValue
    }
  },

  set(key, value) {
    try {
      const serializedValue = typeof value === 'string' ? value : JSON.stringify(value)
      window.sessionStorage.setItem(key, serializedValue)
      return true
    } catch (error) {
      console.warn(`Error setting item in sessionStorage: ${key}`, error)
      return false
    }
  },

  remove(key) {
    try {
      window.sessionStorage.removeItem(key)
      return true
    } catch (error) {
      console.warn(`Error removing item from sessionStorage: ${key}`, error)
      return false
    }
  },

  clear() {
    try {
      window.sessionStorage.clear()
      return true
    } catch (error) {
      console.warn('Error clearing sessionStorage', error)
      return false
    }
  },

  has(key) {
    try {
      return window.sessionStorage.getItem(key) !== null
    } catch (error) {
      console.warn(`Error checking sessionStorage key: ${key}`, error)
      return false
    }
  }
}

/**
 * Cookie utilities
 */
export const cookies = {
  get(name) {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop().split(';').shift()
    return null
  },

  set(name, value, days = 7) {
    const expires = new Date()
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`
  },

  remove(name) {
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/`
  }
}

export default storage
