{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "tsc && vite build", "lint": "eslint . --ext js,jsx,ts,tsx", "preview": "vite preview", "clean": "rimraf node_modules/.vite", "restart": "node clean-restart.js", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.3.0", "@tanstack/react-query": "^5.75.7", "axios": "^1.9.0", "canvas-confetti": "^1.9.3", "date-fns": "^4.1.0", "emoji-picker-react": "^4.12.2", "formik": "^2.4.6", "framer-motion": "^12.10.5", "hls.js": "^1.6.2", "notistack": "^3.0.2", "react": "^19.1.0", "react-confetti": "^6.4.0", "react-dom": "^19.1.0", "react-draggable": "^4.4.6", "react-dropzone": "^14.3.8", "react-image-crop": "^11.0.10", "react-router-dom": "^7.6.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/node": "^20.0.0", "@types/jest": "^29.0.0", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.5.0", "typescript": "^5.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0", "msw": "^2.0.0", "@storybook/react": "^7.0.0", "@storybook/react-vite": "^7.0.0", "@storybook/addon-essentials": "^7.0.0", "@storybook/addon-interactions": "^7.0.0", "@storybook/addon-links": "^7.0.0", "@storybook/addon-docs": "^7.0.0", "@storybook/addon-controls": "^7.0.0", "@storybook/addon-viewport": "^7.0.0", "@storybook/addon-backgrounds": "^7.0.0", "@storybook/addon-measure": "^7.0.0", "@storybook/addon-outline": "^7.0.0", "@storybook/addon-a11y": "^7.0.0", "ts-jest": "^29.0.0", "babel-jest": "^29.0.0", "@babel/preset-env": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.0.0", "jest-watch-typeahead": "^2.0.0", "identity-obj-proxy": "^3.0.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "rimraf": "^6.0.1", "vite": "^6.3.5"}}