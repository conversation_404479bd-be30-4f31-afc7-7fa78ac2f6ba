import React, { useState, useRef } from 'react'
import {
  Box,
  IconButton,
  Badge,
  Popover,
  Typography,
  List,
  ListItem,
  Divider,
  Button,
  CircularProgress,
  Chip,
  useTheme
} from '@mui/material'
import {
  Notifications as NotificationsIcon,
  MarkEmailRead,
  Clear,
  Settings
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { useNotifications } from '../../hooks'
import NotificationItem from './NotificationItem'

const NotificationDropdown = () => {
  const [anchorEl, setAnchorEl] = useState(null)
  const theme = useTheme()
  const navigate = useNavigate()
  const {
    notifications,
    unreadCount,
    loading,
    markAllAsRead,
    clearAll,
    getUnreadNotifications
  } = useNotifications()

  const open = Boolean(anchorEl)
  const unreadNotifications = getUnreadNotifications()
  const recentNotifications = notifications.slice(0, 5)

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleMarkAllAsRead = async () => {
    await markAllAsRead()
  }

  const handleClearAll = async () => {
    await clearAll()
    handleClose()
  }

  const handleViewAll = () => {
    navigate('/notifications')
    handleClose()
  }

  const handleSettings = () => {
    navigate('/settings/notifications')
    handleClose()
  }

  return (
    <>
      <IconButton
        color="inherit"
        onClick={handleClick}
        sx={{
          position: 'relative',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.1)'
          }
        }}
      >
        <Badge 
          badgeContent={unreadCount} 
          color="error"
          max={99}
          sx={{
            '& .MuiBadge-badge': {
              fontSize: '0.75rem',
              minWidth: '18px',
              height: '18px'
            }
          }}
        >
          <NotificationsIcon />
        </Badge>
      </IconButton>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        PaperProps={{
          sx: {
            width: 380,
            maxHeight: 500,
            mt: 1,
            borderRadius: 2,
            boxShadow: theme.shadows[8],
            border: `1px solid ${theme.palette.divider}`,
            background: theme.palette.mode === 'dark' 
              ? 'rgba(18, 18, 18, 0.95)' 
              : 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)'
          }
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 2,
            borderBottom: `1px solid ${theme.palette.divider}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" fontWeight="600">
              Notifications
            </Typography>
            {unreadCount > 0 && (
              <Chip
                label={`${unreadCount} new`}
                size="small"
                color="primary"
                sx={{ fontSize: '0.75rem' }}
              />
            )}
          </Box>
          
          <Box display="flex" gap={0.5}>
            {unreadCount > 0 && (
              <IconButton
                size="small"
                onClick={handleMarkAllAsRead}
                title="Mark all as read"
              >
                <MarkEmailRead fontSize="small" />
              </IconButton>
            )}
            <IconButton
              size="small"
              onClick={handleSettings}
              title="Notification settings"
            >
              <Settings fontSize="small" />
            </IconButton>
          </Box>
        </Box>

        {/* Content */}
        <Box sx={{ maxHeight: 350, overflow: 'auto' }}>
          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress size={24} />
            </Box>
          ) : notifications.length === 0 ? (
            <Box p={3} textAlign="center">
              <NotificationsIcon 
                sx={{ 
                  fontSize: 48, 
                  color: 'text.secondary',
                  mb: 1 
                }} 
              />
              <Typography variant="body2" color="text.secondary">
                No notifications yet
              </Typography>
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              {recentNotifications.map((notification, index) => (
                <React.Fragment key={notification.id}>
                  <NotificationItem
                    notification={notification}
                    onClose={handleClose}
                    compact
                  />
                  {index < recentNotifications.length - 1 && (
                    <Divider variant="inset" />
                  )}
                </React.Fragment>
              ))}
            </List>
          )}
        </Box>

        {/* Footer */}
        {notifications.length > 0 && (
          <Box
            sx={{
              p: 1.5,
              borderTop: `1px solid ${theme.palette.divider}`,
              display: 'flex',
              gap: 1
            }}
          >
            <Button
              fullWidth
              variant="outlined"
              size="small"
              onClick={handleViewAll}
            >
              View All
            </Button>
            {notifications.length > 0 && (
              <Button
                fullWidth
                variant="outlined"
                size="small"
                color="error"
                onClick={handleClearAll}
                startIcon={<Clear />}
              >
                Clear All
              </Button>
            )}
          </Box>
        )}
      </Popover>
    </>
  )
}

export default NotificationDropdown
