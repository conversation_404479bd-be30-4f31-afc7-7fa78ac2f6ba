import { useState, useCallback } from 'react'
import { useFormik } from 'formik'
import { useAuth } from '../context/AuthContext'
import { useSnackbar } from 'notistack'
import { validateUsername } from '../utils/validation'

export const useAuthForm = (initialValues, validationSchema, onSuccess) => {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [usernameStatus, setUsernameStatus] = useState({ checking: false, available: null, message: '' })
  const [rememberMe, setRememberMe] = useState(false)

  const { login, register, loading } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  // Debounced username validation
  const checkUsernameAvailability = useCallback(
    debounce(async (username) => {
      if (!username || username.length < 3) {
        setUsernameStatus({ checking: false, available: null, message: '' })
        return
      }

      setUsernameStatus({ checking: true, available: null, message: 'Checking availability...' })

      try {
        const result = await validateUsername(username)
        setUsernameStatus({
          checking: false,
          available: result.isValid,
          message: result.message
        })
      } catch (error) {
        setUsernameStatus({
          checking: false,
          available: false,
          message: 'Error checking username availability'
        })
      }
    }, 500),
    []
  )

  const formik = useFormik({
    initialValues,
    validationSchema,
    onSubmit: async (values, { setSubmitting, setFieldError }) => {
      setIsSubmitting(true)

      try {
        if (values.username) {
          // Register
          await register({
            name: values.name,
            username: values.username,
            email: values.email,
            password: values.password
          })

          // Show success message for registration
          enqueueSnackbar('Account created successfully! Welcome to Let\'s Talk!', {
            variant: 'success',
            autoHideDuration: 4000
          })
        } else {
          // Login
          await login({
            email: values.email,
            password: values.password,
            rememberMe
          })

          // Show success message for login
          enqueueSnackbar('Welcome back! Login successful.', {
            variant: 'success',
            autoHideDuration: 3000
          })
        }

        if (onSuccess) {
          onSuccess()
        }
      } catch (error) {
        console.error('Auth error:', error)

        const errorMessage = error.response?.data?.message || error.message || 'An error occurred'

        // Handle specific field errors with better messaging
        if (errorMessage.toLowerCase().includes('email')) {
          if (errorMessage.toLowerCase().includes('already')) {
            setFieldError('email', 'This email is already registered. Try logging in instead.')
          } else if (errorMessage.toLowerCase().includes('invalid') || errorMessage.toLowerCase().includes('not found')) {
            setFieldError('email', 'No account found with this email address.')
          } else {
            setFieldError('email', errorMessage)
          }
        } else if (errorMessage.toLowerCase().includes('username')) {
          if (errorMessage.toLowerCase().includes('taken') || errorMessage.toLowerCase().includes('already')) {
            setFieldError('username', 'This username is already taken. Please choose another.')
          } else {
            setFieldError('username', errorMessage)
          }
        } else if (errorMessage.toLowerCase().includes('password')) {
          if (errorMessage.toLowerCase().includes('incorrect') || errorMessage.toLowerCase().includes('invalid')) {
            setFieldError('password', 'Incorrect password. Please try again.')
          } else {
            setFieldError('password', errorMessage)
          }
        } else if (errorMessage.toLowerCase().includes('credentials')) {
          setFieldError('email', 'Invalid email or password.')
          setFieldError('password', 'Invalid email or password.')
        } else {
          // Generic error handling
          enqueueSnackbar(errorMessage, {
            variant: 'error',
            autoHideDuration: 5000
          })
        }
      } finally {
        setIsSubmitting(false)
        setSubmitting(false)
      }
    }
  })

  // Handle username field changes with availability checking
  const handleUsernameChange = (event) => {
    const username = event.target.value
    formik.handleChange(event)

    if (username && username.length >= 3) {
      checkUsernameAvailability(username)
    } else {
      setUsernameStatus({ checking: false, available: null, message: '' })
    }
  }

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword)
  }

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword)
  }

  const handleRememberMeChange = (event) => {
    setRememberMe(event.target.checked)
  }

  return {
    formik,
    isSubmitting: isSubmitting || loading,
    showPassword,
    showConfirmPassword,
    usernameStatus,
    rememberMe,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    handleUsernameChange,
    handleRememberMeChange
  }
}

// Debounce utility function
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

export default useAuthForm
