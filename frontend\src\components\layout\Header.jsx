import {
  AppBar,
  Too<PERSON>bar,
  Typography,
  IconButton,
  Box,
  InputBase,
  Avatar,
  useTheme,
  alpha
} from '@mui/material'
import {
  Menu as MenuIcon,
  Search as SearchIcon,
  Settings,
  DarkMode,
  LightMode
} from '@mui/icons-material'
import { useAuth } from '../../context/AuthContext'
import { NotificationDropdown } from '../notifications'

const Header = ({
  onSidebarToggle,
  onRightSidebarToggle,
  sidebarOpen,
  rightSidebarOpen,
  showRightSidebarToggle = true
}) => {
  const theme = useTheme()
  const { user } = useAuth()

  return (
    <AppBar
      position="sticky"
      elevation={1}
      sx={{
        bgcolor: 'background.paper',
        color: 'text.primary',
        borderBottom: 1,
        borderColor: 'divider'
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between' }}>
        {/* Left Section */}
        <Box display="flex" alignItems="center">
          <IconButton
            edge="start"
            onClick={onSidebarToggle}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" component="div" sx={{ display: { xs: 'none', sm: 'block' } }}>
            Let's Talk
          </Typography>
        </Box>

        {/* Center Section - Search */}
        <Box
          sx={{
            position: 'relative',
            borderRadius: 1,
            backgroundColor: alpha(theme.palette.common.white, 0.15),
            '&:hover': {
              backgroundColor: alpha(theme.palette.common.white, 0.25),
            },
            marginLeft: 0,
            width: '100%',
            maxWidth: 400,
            [theme.breakpoints.up('sm')]: {
              marginLeft: theme.spacing(3),
              width: 'auto',
            },
          }}
        >
          <Box
            sx={{
              padding: theme.spacing(0, 2),
              height: '100%',
              position: 'absolute',
              pointerEvents: 'none',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <SearchIcon />
          </Box>
          <InputBase
            placeholder="Search…"
            sx={{
              color: 'inherit',
              '& .MuiInputBase-input': {
                padding: theme.spacing(1, 1, 1, 0),
                paddingLeft: `calc(1em + ${theme.spacing(4)})`,
                transition: theme.transitions.create('width'),
                width: '100%',
                [theme.breakpoints.up('md')]: {
                  width: '20ch',
                },
              },
            }}
          />
        </Box>

        {/* Right Section */}
        <Box display="flex" alignItems="center" gap={1}>
          <NotificationDropdown />

          <IconButton color="inherit">
            {theme.palette.mode === 'dark' ? <LightMode /> : <DarkMode />}
          </IconButton>

          <IconButton color="inherit">
            <Settings />
          </IconButton>

          {showRightSidebarToggle && (
            <IconButton
              color="inherit"
              onClick={onRightSidebarToggle}
            >
              <MenuIcon />
            </IconButton>
          )}

          <Avatar
            src={user?.avatar}
            sx={{ width: 32, height: 32, ml: 1 }}
          >
            {user?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
        </Box>
      </Toolbar>
    </AppBar>
  )
}

export default Header
