const Notification = require('../models/Notification');
const User = require('../models/User');
const { createError } = require('../utils/error');
const notificationService = require('../services/notificationService');

/**
 * Create a new notification
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createNotification = async (req, res, next) => {
  try {
    const { recipient, type, title, message, data, priority = 'normal' } = req.body;

    // Don't create notification if sender is the recipient
    if (req.user.id === recipient) {
      return res.status(200).json({
        success: true,
        message: 'Notification not created (self-action)',
      });
    }

    // Use notification service to create and send notification
    const notification = await notificationService.sendNotification(recipient, {
      type,
      title,
      message,
      data: {
        ...data,
        senderId: req.user.id
      },
      priority
    });

    res.status(201).json({
      success: true,
      data: notification,
      message: 'Notification created successfully'
    });
  } catch (err) {
    console.error('Error creating notification:', err);
    next(err);
  }
};

/**
 * Get all notifications for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getNotifications = async (req, res, next) => {
  try {
    const { page = 1, limit = 20, unreadOnly = false, type } = req.query;

    // Build query
    let query = { recipient: req.user.id };
    if (unreadOnly === 'true') {
      query.read = false;
    }
    if (type) {
      query.type = type;
    }

    // Get notifications with pagination
    const notifications = await Notification.find(query)
      .sort({ createdAt: -1 })
      .populate('sender', 'username name profilePicture')
      .populate('post', 'media caption')
      .populate('reel', 'video thumbnail title')
      .populate('comment', 'text')
      .populate('mood', 'content emotion')
      .populate('story', 'media')
      .limit(limit * 1)
      .skip((page - 1) * limit);

    // Get total count and unread count
    const total = await Notification.countDocuments(query);
    const unreadCount = await Notification.countDocuments({
      recipient: req.user.id,
      read: false
    });

    // Format notifications for frontend
    const formattedNotifications = notifications.map(notification => ({
      id: notification._id,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      read: notification.read,
      priority: notification.priority,
      createdAt: notification.createdAt,
      timeAgo: notification.timeAgo,
      actionUrl: notification.actionUrl,
      actionText: notification.actionText,
      data: notification.data,
      sender: notification.sender ? {
        id: notification.sender._id,
        username: notification.sender.username,
        name: notification.sender.name,
        profilePicture: notification.sender.profilePicture
      } : null,
      relatedContent: {
        post: notification.post,
        reel: notification.reel,
        comment: notification.comment,
        mood: notification.mood,
        story: notification.story
      }
    }));

    res.status(200).json({
      success: true,
      data: formattedNotifications,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      },
      unreadCount
    });
  } catch (err) {
    console.error('Error getting notifications:', err);
    next(err);
  }
};

/**
 * Get unread notifications count for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUnreadCount = async (req, res, next) => {
  try {
    const count = await Notification.countDocuments({
      recipient: req.user.id,
      read: false,
    });

    res.status(200).json({
      success: true,
      data: { count }
    });
  } catch (err) {
    console.error('Error getting unread count:', err);
    next(err);
  }
};

/**
 * Mark a notification as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.markAsRead = async (req, res, next) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return next(createError(404, 'Notification not found'));
    }

    // Check if the notification belongs to the authenticated user
    if (notification.recipient.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to access this notification'));
    }

    // Update notification
    notification.read = true;
    await notification.save();

    // Emit notification read event
    socketEmitter.emitNotificationRead(req.user.id, notification._id);

    res.status(200).json({
      success: true,
      data: notification,
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Mark all notifications as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.markAllAsRead = async (req, res, next) => {
  try {
    await Notification.updateMany(
      { recipient: req.user.id, read: false },
      { read: true }
    );

    // Emit all notifications read event
    socketEmitter.emitAllNotificationsRead(req.user.id);

    res.status(200).json({
      success: true,
      message: 'All notifications marked as read',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a notification
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteNotification = async (req, res, next) => {
  try {
    const notification = await Notification.findById(req.params.id);

    if (!notification) {
      return next(createError(404, 'Notification not found'));
    }

    // Check if the notification belongs to the authenticated user
    if (notification.recipient.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to delete this notification'));
    }

    await notification.remove();

    res.status(200).json({
      success: true,
      message: 'Notification deleted',
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete all notifications for the authenticated user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteAllNotifications = async (req, res, next) => {
  try {
    await Notification.deleteMany({ recipient: req.user.id });

    res.status(200).json({
      success: true,
      message: 'All notifications deleted',
    });
  } catch (err) {
    next(err);
  }
};
