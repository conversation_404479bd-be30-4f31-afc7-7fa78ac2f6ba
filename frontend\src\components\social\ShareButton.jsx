import { useState } from 'react'
import { 
  IconButton, 
  Menu, 
  MenuItem, 
  ListItemIcon, 
  ListItemText,
  Typography,
  Box
} from '@mui/material'
import { 
  Share, 
  Link as LinkIcon, 
  Twitter, 
  Facebook,
  WhatsApp,
  ContentCopy
} from '@mui/icons-material'
import { useSnackbar } from 'notistack'

const ShareButton = ({ post, onShare }) => {
  const [anchorEl, setAnchorEl] = useState(null)
  const { enqueueSnackbar } = useSnackbar()
  const open = Boolean(anchorEl)

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  const handleClose = () => {
    setAnchorEl(null)
  }

  const handleCopyLink = async () => {
    try {
      const url = `${window.location.origin}/posts/${post.id}`
      await navigator.clipboard.writeText(url)
      enqueueSnackbar('Link copied to clipboard!', { variant: 'success' })
    } catch (error) {
      enqueueSnackbar('Failed to copy link', { variant: 'error' })
    }
    handleClose()
  }

  const handleShareToSocial = (platform) => {
    const url = `${window.location.origin}/posts/${post.id}`
    const text = post.content ? post.content.substring(0, 100) + '...' : 'Check out this post!'
    
    let shareUrl = ''
    
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`
        break
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`
        break
      case 'whatsapp':
        shareUrl = `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`
        break
      default:
        return
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=400')
    handleClose()
    onShare?.()
  }

  return (
    <Box display="flex" alignItems="center">
      <IconButton onClick={handleClick} sx={{ ml: 1 }}>
        <Share />
      </IconButton>
      
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        transformOrigin={{ horizontal: 'left', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'left', vertical: 'bottom' }}
      >
        <MenuItem onClick={handleCopyLink}>
          <ListItemIcon>
            <ContentCopy fontSize="small" />
          </ListItemIcon>
          <ListItemText>Copy Link</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleShareToSocial('twitter')}>
          <ListItemIcon>
            <Twitter fontSize="small" />
          </ListItemIcon>
          <ListItemText>Share on Twitter</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleShareToSocial('facebook')}>
          <ListItemIcon>
            <Facebook fontSize="small" />
          </ListItemIcon>
          <ListItemText>Share on Facebook</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={() => handleShareToSocial('whatsapp')}>
          <ListItemIcon>
            <WhatsApp fontSize="small" />
          </ListItemIcon>
          <ListItemText>Share on WhatsApp</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  )
}

export default ShareButton
