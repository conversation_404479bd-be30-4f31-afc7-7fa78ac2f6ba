/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {boolean} True if password meets minimum requirements
 */
const validatePassword = (password) => {
  if (!password || typeof password !== 'string') {
    return false
  }

  // Minimum requirements
  const minLength = 8
  const hasUpperCase = /[A-Z]/.test(password)
  const hasLowerCase = /[a-z]/.test(password)
  const hasNumbers = /\d/.test(password)
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

  return (
    password.length >= minLength &&
    hasUpperCase &&
    hasLowerCase &&
    hasNumbers &&
    hasSpecialChar
  )
}

/**
 * Get password validation errors
 * @param {string} password - Password to validate
 * @returns {string[]} Array of error messages
 */
export const getPasswordValidationErrors = (password) => {
  const errors = []

  if (!password) {
    errors.push('Password is required')
    return errors
  }

  if (typeof password !== 'string') {
    errors.push('Password must be a string')
    return errors
  }

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }

  return errors
}

/**
 * Get password strength score (0-5)
 * @param {string} password - Password to evaluate
 * @returns {number} Strength score from 0 (weakest) to 5 (strongest)
 */
export const getPasswordStrength = (password) => {
  if (!password) return 0

  let score = 0

  // Length check
  if (password.length >= 8) score++
  if (password.length >= 12) score++

  // Character variety checks
  if (/[a-z]/.test(password)) score++
  if (/[A-Z]/.test(password)) score++
  if (/\d/.test(password)) score++
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++

  // Bonus for very long passwords
  if (password.length >= 16) score++

  return Math.min(score, 5)
}

/**
 * Get password strength label
 * @param {string} password - Password to evaluate
 * @returns {string} Strength label
 */
export const getPasswordStrengthLabel = (password) => {
  const strength = getPasswordStrength(password)
  
  switch (strength) {
    case 0:
    case 1:
      return 'Very Weak'
    case 2:
      return 'Weak'
    case 3:
      return 'Fair'
    case 4:
      return 'Good'
    case 5:
      return 'Strong'
    default:
      return 'Unknown'
  }
}

export default validatePassword
