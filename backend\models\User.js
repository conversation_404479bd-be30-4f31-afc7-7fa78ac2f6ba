const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const UserSchema = new mongoose.Schema({
  username: {
    type: String,
    required: [true, 'Please provide a username'],
    unique: true,
    trim: true,
    minlength: [3, 'Username must be at least 3 characters'],
    maxlength: [20, 'Username cannot be more than 20 characters'],
  },
  email: {
    type: String,
    required: [true, 'Please provide an email'],
    unique: true,
    match: [
      /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      'Please provide a valid email',
    ],
  },
  password: {
    type: String,
    required: [true, 'Please provide a password'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false,
  },
  name: {
    type: String,
    required: [true, 'Please provide your name'],
  },
  fullName: {
    type: String,
    default: function() {
      return this.name;
    }
  },
  bio: {
    type: String,
    maxlength: [150, 'Bio cannot be more than 150 characters'],
    default: '',
  },
  phone: {
    type: String,
    default: '',
  },
  gender: {
    type: String,
    enum: ['male', 'female', 'other', 'prefer-not-to-say'],
    default: 'prefer-not-to-say',
  },
  isVerified: {
    type: Boolean,
    default: false,
  },
  isOnline: {
    type: Boolean,
    default: false,
  },
  lastActive: {
    type: Date,
    default: Date.now,
  },
  avatar: {
    type: String,
    default: '/default-profile.svg',
  },
  coverImage: {
    type: String,
    default: '',
  },
  website: {
    type: String,
    default: '',
  },
  profilePicture: {
    type: String,
    default: '/default-profile.svg',
  },
  currentMood: {
    mood: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Mood',
    },
    intensity: {
      type: Number,
      min: 1,
      max: 10,
      default: 5,
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  wellnessSettings: {
    contentFilters: {
      hideNegativeContent: {
        type: Boolean,
        default: false,
      },
      preferredEmotions: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
      }],
      avoidedEmotions: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Emotion',
      }],
    },
    timeLimit: {
      enabled: {
        type: Boolean,
        default: false,
      },
      dailyLimitMinutes: {
        type: Number,
        default: 60,
      },
    },
    hideMetrics: {
      type: Boolean,
      default: false,
    },
    wellnessReminders: {
      enabled: {
        type: Boolean,
        default: true,
      },
      frequency: {
        type: String,
        enum: ['hourly', 'every2hours', 'every3hours', 'every4hours'],
        default: 'every2hours',
      },
    },
  },
  isPrivate: {
    type: Boolean,
    default: false,
  },
  // Privacy Settings
  showEmail: {
    type: Boolean,
    default: false,
  },
  showPhone: {
    type: Boolean,
    default: false,
  },
  allowMessages: {
    type: String,
    enum: ['everyone', 'followers', 'none'],
    default: 'everyone',
  },
  allowTagging: {
    type: Boolean,
    default: true,
  },
  allowMentions: {
    type: Boolean,
    default: true,
  },
  showOnlineStatus: {
    type: Boolean,
    default: true,
  },
  allowLocationTracking: {
    type: Boolean,
    default: false,
  },
  // User Preferences
  language: {
    type: String,
    default: 'en',
  },
  timezone: {
    type: String,
    default: 'UTC',
  },
  dateFormat: {
    type: String,
    enum: ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'],
    default: 'MM/DD/YYYY',
  },
  timeFormat: {
    type: String,
    enum: ['12h', '24h'],
    default: '12h',
  },
  currency: {
    type: String,
    default: 'USD',
  },
  autoPlayVideos: {
    type: Boolean,
    default: true,
  },
  showSensitiveContent: {
    type: Boolean,
    default: false,
  },
  // Theme Preferences
  themePreference: {
    mode: {
      type: String,
      enum: ['light', 'dark', 'system'],
      default: 'light',
    },
    primaryColor: {
      type: String,
      default: '#1976d2',
    },
    accentColor: {
      type: String,
      default: '#e91e63',
    },
    updatedAt: {
      type: Date,
      default: Date.now,
    },
  },
  // Security Settings
  twoFactorEnabled: {
    type: Boolean,
    default: false,
  },
  loginAlerts: {
    type: Boolean,
    default: true,
  },
  sessionTimeout: {
    type: Number,
    default: 30, // minutes
  },
  allowedDevices: {
    type: String,
    enum: ['unlimited', 'limited'],
    default: 'unlimited',
  },
  // Account Status
  isDeleted: {
    type: Boolean,
    default: false,
  },
  deletedAt: {
    type: Date,
  },
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number],
      default: [0, 0]
    },
    name: {
      type: String,
      default: ''
    }
  },
  role: {
    type: String,
    enum: ['user', 'vendor', 'admin'],
    default: 'user',
  },
  followingVendors: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Vendor'
  }],
  coins: {
    type: Number,
    default: 0,
  },
  wallet: {
    address: {
      type: String,
    },
    connectedAt: {
      type: Date,
    },
    blockchain: {
      type: String,
      enum: ['ethereum', 'polygon', 'solana', 'binance'],
    },
  },
  liveStreamPreferences: {
    defaultViewingMode: {
      type: String,
      enum: ['default', 'audio-only', 'mini-player', 'background', 'swipe', 'silent', 'asmr', 'tutorial', 'panel'],
      default: 'default'
    },
    audioOnlyQuality: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium'
    },
    backgroundModeEnabled: {
      type: Boolean,
      default: true
    },
    miniPlayerPosition: {
      x: {
        type: Number,
        default: 20
      },
      y: {
        type: Number,
        default: 20
      }
    },
    creatorModeSettings: {
      type: Object,
      default: {}
    }
  },
  fanRewardsEnabled: {
    type: Boolean,
    default: true
  },
  fanRewardsSettings: {
    pointsPerMinuteWatched: {
      type: Number,
      default: 1
    },
    pointsPerChat: {
      type: Number,
      default: 2
    },
    pointsPerReaction: {
      type: Number,
      default: 1
    },
    pointsPerGift: {
      type: Number,
      default: 5
    },
    pointsPerShare: {
      type: Number,
      default: 10
    },
    levelUpThresholds: [Number],
    customBadges: [{
      name: String,
      description: String,
      imageUrl: String,
      pointThreshold: Number
    }],
    customRewards: [{
      name: String,
      description: String,
      imageUrl: String,
      pointCost: Number,
      type: {
        type: String,
        enum: ['emote', 'badge', 'effect', 'access', 'discount', 'physical', 'custom'],
      },
      isActive: {
        type: Boolean,
        default: true
      }
    }]
  },
  voiceCommandsEnabled: {
    type: Boolean,
    default: false
  },
  voiceCommandSettings: {
    language: {
      type: String,
      default: 'en-US'
    },
    sensitivity: {
      type: Number,
      min: 0,
      max: 1,
      default: 0.7
    },
    wakeWord: {
      type: String,
      default: 'Hey Stream'
    },
    preferredVoice: {
      type: String,
      enum: ['default', 'male', 'female', 'neutral'],
      default: 'default'
    },
    enabledCommands: {
      type: [String],
      default: ['mute_chat', 'highlight_clip', 'take_screenshot', 'toggle_camera', 'toggle_microphone']
    }
  },
  nftCollections: [{
    contractAddress: String,
    blockchain: {
      type: String,
      enum: ['ethereum', 'polygon', 'solana', 'binance'],
    },
    name: String,
    description: String,
    imageUrl: String,
    tokenCount: Number,
    isVerified: {
      type: Boolean,
      default: false
    }
  }],
  themePreference: {
    mode: {
      type: String,
      enum: ['light', 'dark', 'system'],
      default: 'light'
    },
    updatedAt: {
      type: Date,
      default: Date.now
    }
  },
  resetPasswordToken: String,
  resetPasswordExpire: Date,
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Create index for location
UserSchema.index({ location: '2dsphere' });

// Virtual fields for followers and following counts
UserSchema.virtual('followersCount', {
  ref: 'Follow',
  localField: '_id',
  foreignField: 'following',
  count: true,
});

UserSchema.virtual('followingCount', {
  ref: 'Follow',
  localField: '_id',
  foreignField: 'follower',
  count: true,
});

// Encrypt password using bcrypt
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    return next();
  }

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Sign JWT and return
UserSchema.methods.getSignedJwtToken = function () {
  return jwt.sign({ id: this._id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE,
  });
};

// Match user entered password to hashed password in database
UserSchema.methods.matchPassword = async function (enteredPassword) {
  try {
    return await bcrypt.compare(enteredPassword, this.password);
  } catch (error) {
    console.error('Password comparison error:', error);
    throw error;
  }
};

// Get public profile (without sensitive data)
UserSchema.methods.getPublicProfile = function () {
  const userObject = this.toObject();

  // Remove sensitive fields
  delete userObject.password;
  delete userObject.resetPasswordToken;
  delete userObject.resetPasswordExpire;

  return {
    _id: userObject._id,
    id: userObject._id,
    username: userObject.username,
    email: userObject.email,
    name: userObject.name,
    fullName: userObject.fullName,
    bio: userObject.bio,
    website: userObject.website,
    profilePicture: userObject.profilePicture,
    coverImage: userObject.coverImage,
    currentMood: userObject.currentMood,
    isVerified: userObject.isVerified,
    isPrivate: userObject.isPrivate,
    isOnline: userObject.isOnline,
    lastActive: userObject.lastActive,
    role: userObject.role,
    coins: userObject.coins,
    themePreference: userObject.themePreference,
    createdAt: userObject.createdAt,
    updatedAt: userObject.updatedAt,
    followersCount: userObject.followersCount,
    followingCount: userObject.followingCount
  };
};

module.exports = mongoose.model('User', UserSchema);
