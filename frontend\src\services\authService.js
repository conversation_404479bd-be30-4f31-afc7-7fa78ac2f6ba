import axios from '../utils/fixedAxios'

class AuthService {
  // Login user
  async login(credentials) {
    const response = await axios.post('/api/auth/login', credentials)
    const { token, user } = response.data
    
    if (token) {
      localStorage.setItem('token', token)
    }
    
    return { token, user }
  }

  // Register user
  async register(userData) {
    const response = await axios.post('/api/auth/register', userData)
    const { token, user } = response.data
    
    if (token) {
      localStorage.setItem('token', token)
    }
    
    return { token, user }
  }

  // Logout user
  async logout() {
    try {
      await axios.post('/api/auth/logout')
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error)
    } finally {
      localStorage.removeItem('token')
    }
  }

  // Get current user
  async getCurrentUser() {
    const response = await axios.get('/api/auth/me')
    return response.data.data || response.data.user
  }

  // Refresh token
  async refreshToken() {
    const response = await axios.post('/api/auth/refresh-token')
    const { token } = response.data
    
    if (token) {
      localStorage.setItem('token', token)
    }
    
    return token
  }

  // Forgot password
  async forgotPassword(email) {
    const response = await axios.post('/api/auth/forgot-password', { email })
    return response.data
  }

  // Reset password
  async resetPassword(token, password) {
    const response = await axios.post('/api/auth/reset-password', {
      token,
      password
    })
    return response.data
  }

  // Change password
  async changePassword(currentPassword, newPassword) {
    const response = await axios.post('/api/auth/change-password', {
      currentPassword,
      newPassword
    })
    return response.data
  }

  // Verify email
  async verifyEmail(token) {
    const response = await axios.post('/api/auth/verify-email', { token })
    return response.data
  }

  // Resend verification email
  async resendVerificationEmail() {
    const response = await axios.post('/api/auth/resend-verification')
    return response.data
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!localStorage.getItem('token')
  }

  // Get stored token
  getToken() {
    return localStorage.getItem('token')
  }

  // Update profile
  async updateProfile(profileData) {
    const formData = new FormData()
    
    Object.keys(profileData).forEach(key => {
      if (profileData[key] !== null && profileData[key] !== undefined) {
        if (key === 'avatar' && profileData[key] instanceof File) {
          formData.append('avatar', profileData[key])
        } else if (key === 'banner' && profileData[key] instanceof File) {
          formData.append('banner', profileData[key])
        } else {
          formData.append(key, profileData[key])
        }
      }
    })

    const response = await axios.put('/api/users/me', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data.data || response.data.user
  }

  // Delete account
  async deleteAccount(password) {
    const response = await axios.delete('/api/auth/account', {
      data: { password }
    })
    localStorage.removeItem('token')
    return response.data
  }

  // Enable 2FA
  async enable2FA() {
    const response = await axios.post('/api/auth/2fa/enable')
    return response.data
  }

  // Disable 2FA
  async disable2FA(code) {
    const response = await axios.post('/api/auth/2fa/disable', { code })
    return response.data
  }

  // Verify 2FA
  async verify2FA(code) {
    const response = await axios.post('/api/auth/2fa/verify', { code })
    return response.data
  }
}

export default new AuthService()
