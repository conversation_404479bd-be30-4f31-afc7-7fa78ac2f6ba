import React, { useState } from 'react'
import { Box, Button, Typography, Paper, Alert, CircularProgress } from '@mui/material'
import axios from '../../utils/fixedAxios'

const SettingsDebug = () => {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  const [error, setError] = useState(null)

  const testSettingsEndpoint = async () => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      console.log('Testing settings endpoint...')

      // Check token
      const token = localStorage.getItem('token')
      console.log('Token exists:', !!token)
      if (token) {
        console.log('Token preview:', token.substring(0, 20) + '...')
      }

      // Test the endpoint
      const response = await axios.get('/api/users/settings')
      console.log('Settings response:', response)

      setResult({
        success: true,
        data: response.data,
        status: response.status
      })
    } catch (err) {
      console.error('Settings test error:', err)
      setError({
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        config: {
          url: err.config?.url,
          baseURL: err.config?.baseURL,
          method: err.config?.method
        }
      })
    } finally {
      setLoading(false)
    }
  }

  const testHealthEndpoint = async () => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      console.log('Testing health endpoint...')
      const response = await axios.get('/api/health')
      console.log('Health response:', response)

      setResult({
        success: true,
        data: response.data,
        status: response.status
      })
    } catch (err) {
      console.error('Health test error:', err)
      setError({
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        config: {
          url: err.config?.url,
          baseURL: err.config?.baseURL,
          method: err.config?.method
        }
      })
    } finally {
      setLoading(false)
    }
  }

  const testLoginEndpoint = async () => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      console.log('Testing login endpoint with test credentials...')
      const response = await axios.post('/api/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      })
      console.log('Login response:', response)

      // Store the token if login is successful
      if (response.data.token) {
        localStorage.setItem('token', response.data.token)
        console.log('Token stored:', response.data.token.substring(0, 20) + '...')
      }

      setResult({
        success: true,
        data: response.data,
        status: response.status
      })
    } catch (err) {
      console.error('Login test error:', err)
      setError({
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        config: {
          url: err.config?.url,
          baseURL: err.config?.baseURL,
          method: err.config?.method
        }
      })
    } finally {
      setLoading(false)
    }
  }

  const testRegisterEndpoint = async () => {
    try {
      setLoading(true)
      setError(null)
      setResult(null)

      console.log('Testing register endpoint...')
      const response = await axios.post('/api/auth/register', {
        username: 'testuser' + Date.now(),
        email: 'test' + Date.now() + '@example.com',
        password: 'password123',
        name: 'Test User',
        fullName: 'Test User'
      })
      console.log('Register response:', response)

      // Store the token if registration is successful
      if (response.data.token) {
        localStorage.setItem('token', response.data.token)
        console.log('Token stored:', response.data.token.substring(0, 20) + '...')
      }

      setResult({
        success: true,
        data: response.data,
        status: response.status
      })
    } catch (err) {
      console.error('Register test error:', err)
      setError({
        message: err.message,
        status: err.response?.status,
        data: err.response?.data,
        config: {
          url: err.config?.url,
          baseURL: err.config?.baseURL,
          method: err.config?.method
        }
      })
    } finally {
      setLoading(false)
    }
  }

  const checkDynamicConfig = () => {
    try {
      // Check if dynamic config is loaded
      const configScript = document.getElementById('dynamic-config')
      if (configScript) {
        const config = JSON.parse(configScript.textContent)
        console.log('Dynamic config from script tag:', config)
        setResult({
          success: true,
          data: { dynamicConfig: config },
          status: 200
        })
      } else if (window.dynamicConfig) {
        console.log('Dynamic config from window:', window.dynamicConfig)
        setResult({
          success: true,
          data: { dynamicConfig: window.dynamicConfig },
          status: 200
        })
      } else {
        setError({
          message: 'No dynamic config found',
          status: 404
        })
      }
    } catch (err) {
      setError({
        message: err.message,
        status: 500
      })
    }
  }

  const reloadPage = () => {
    window.location.reload()
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Settings Debug Tool
      </Typography>

      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button
          variant="contained"
          onClick={testHealthEndpoint}
          disabled={loading}
        >
          Test Health Endpoint
        </Button>

        <Button
          variant="contained"
          onClick={testRegisterEndpoint}
          disabled={loading}
          color="secondary"
        >
          Test Register
        </Button>

        <Button
          variant="contained"
          onClick={testLoginEndpoint}
          disabled={loading}
          color="success"
        >
          Test Login
        </Button>

        <Button
          variant="contained"
          onClick={testSettingsEndpoint}
          disabled={loading}
          color="warning"
        >
          Test Settings Endpoint
        </Button>

        <Button
          variant="outlined"
          onClick={checkDynamicConfig}
          disabled={loading}
        >
          Check Dynamic Config
        </Button>

        <Button
          variant="outlined"
          onClick={reloadPage}
          disabled={loading}
          color="info"
        >
          Reload Page
        </Button>
      </Box>

      {loading && (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <CircularProgress size={20} />
          <Typography>Testing...</Typography>
        </Box>
      )}

      {result && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: 'success.light' }}>
          <Typography variant="h6" color="success.dark">
            Success (Status: {result.status})
          </Typography>
          <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
            {JSON.stringify(result.data, null, 2)}
          </pre>
        </Paper>
      )}

      {error && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: 'error.light' }}>
          <Typography variant="h6" color="error.dark">
            Error (Status: {error.status})
          </Typography>
          <Typography variant="body2" color="error.dark" sx={{ mb: 1 }}>
            {error.message}
          </Typography>
          {error.config && (
            <Typography variant="caption" display="block" sx={{ mb: 1 }}>
              Request: {error.config.method?.toUpperCase()} {error.config.baseURL}{error.config.url}
            </Typography>
          )}
          {error.data && (
            <pre style={{ whiteSpace: 'pre-wrap', fontSize: '12px' }}>
              {JSON.stringify(error.data, null, 2)}
            </pre>
          )}
        </Paper>
      )}

      <Alert severity="info" sx={{ mt: 2 }}>
        <Typography variant="body2">
          This debug tool helps identify issues with the settings endpoint.
          Check the browser console for detailed logs.
        </Typography>
      </Alert>
    </Box>
  )
}

export default SettingsDebug
