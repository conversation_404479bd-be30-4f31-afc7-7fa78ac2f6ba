import React from 'react'
import { motion } from 'framer-motion'
import { Box, Button, useTheme, useMediaQuery } from '@mui/material'
import { Google, Facebook, Twitter } from '@mui/icons-material'

const SocialLogin = () => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  const socialProviders = [
    {
      name: 'Google',
      icon: Google,
      color: '#db4437',
      hoverColor: '#c23321'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: '#4267B2',
      hoverColor: '#365899'
    },
    {
      name: 'Twitter',
      icon: Twitter,
      color: '#1DA1F2',
      hoverColor: '#0d8bd9'
    }
  ]

  const handleSocialLogin = (provider) => {
    // TODO: Implement social login
    console.log(`Login with ${provider}`)
    // This would typically redirect to OAuth provider
    // window.location.href = `/api/auth/${provider.toLowerCase()}`
  }

  return (
    <Box sx={{ mb: 2 }}>
      {/* Social Login Buttons */}
      <Box
        sx={{
          display: 'flex',
          gap: isMobile ? 1.5 : 2,
          mb: 3,
          flexDirection: isMobile ? 'column' : 'row'
        }}
      >
        {socialProviders.map((provider, index) => {
          const IconComponent = provider.icon

          return (
            <motion.div
              key={provider.name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              style={{ flex: 1 }}
            >
              <Button
                fullWidth
                variant="outlined"
                onClick={() => handleSocialLogin(provider.name)}
                startIcon={<IconComponent />}
                sx={{
                  py: isMobile ? 1.2 : 1.5,
                  borderRadius: 2,
                  borderColor: theme.palette.divider,
                  color: theme.palette.text.primary,
                  backgroundColor: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.02)',
                  fontSize: isMobile ? '0.875rem' : '1rem',
                  '&:hover': {
                    borderColor: provider.color,
                    backgroundColor: `${provider.color}10`,
                    color: provider.color,
                    transform: 'translateY(-2px)',
                    boxShadow: `0 4px 12px ${provider.color}30`
                  },
                  transition: 'all 0.2s ease',
                  textTransform: 'none',
                  fontWeight: 500
                }}
              >
                {isMobile ? provider.name : `Continue with ${provider.name}`}
              </Button>
            </motion.div>
          )
        })}
      </Box>

      {/* Quick Login with Google (Featured) */}
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.3 }}
      >
        <Button
          fullWidth
          variant="contained"
          onClick={() => handleSocialLogin('Google')}
          startIcon={<Google />}
          sx={{
            py: isMobile ? 1.2 : 1.5,
            borderRadius: 2,
            background: 'linear-gradient(45deg, #4285f4 30%, #34a853 90%)',
            color: 'white',
            fontWeight: 600,
            textTransform: 'none',
            fontSize: isMobile ? '0.875rem' : '1rem',
            boxShadow: '0 4px 12px rgba(66, 133, 244, 0.3)',
            '&:hover': {
              background: 'linear-gradient(45deg, #3367d6 30%, #2d8f47 90%)',
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 16px rgba(66, 133, 244, 0.4)'
            },
            transition: 'all 0.2s ease'
          }}
        >
          {isMobile ? 'Google Sign In' : 'Continue with Google'}
        </Button>
      </motion.div>
    </Box>
  )
}

export default SocialLogin
