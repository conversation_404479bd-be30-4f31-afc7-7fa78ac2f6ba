/**
 * Debounce function - delays execution until after wait milliseconds have elapsed
 * since the last time the debounced function was invoked
 * @param {Function} func - Function to debounce
 * @param {number} wait - Number of milliseconds to delay
 * @param {boolean} immediate - Trigger on leading edge instead of trailing
 * @returns {Function} Debounced function
 */
const debounce = (func, wait, immediate = false) => {
  let timeout

  return function executedFunction(...args) {
    const later = () => {
      timeout = null
      if (!immediate) func.apply(this, args)
    }

    const callNow = immediate && !timeout

    clearTimeout(timeout)
    timeout = setTimeout(later, wait)

    if (callNow) func.apply(this, args)
  }
}

/**
 * Create a debounced version of an async function
 * @param {Function} func - Async function to debounce
 * @param {number} wait - Number of milliseconds to delay
 * @returns {Function} Debounced async function
 */
export const debounceAsync = (func, wait) => {
  let timeout
  let resolveList = []
  let rejectList = []

  return function executedFunction(...args) {
    return new Promise((resolve, reject) => {
      resolveList.push(resolve)
      rejectList.push(reject)

      clearTimeout(timeout)
      timeout = setTimeout(async () => {
        try {
          const result = await func.apply(this, args)
          resolveList.forEach(r => r(result))
        } catch (error) {
          rejectList.forEach(r => r(error))
        } finally {
          resolveList = []
          rejectList = []
        }
      }, wait)
    })
  }
}

/**
 * Debounce with cancel functionality
 * @param {Function} func - Function to debounce
 * @param {number} wait - Number of milliseconds to delay
 * @returns {Object} Object with debounced function and cancel method
 */
export const debounceWithCancel = (func, wait) => {
  let timeout

  const debouncedFunction = function executedFunction(...args) {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(this, args), wait)
  }

  const cancel = () => {
    clearTimeout(timeout)
  }

  return {
    debounced: debouncedFunction,
    cancel
  }
}

export default debounce
