import { useState } from 'react'
import { IconButton, Typography, Box } from '@mui/material'
import { Favorite, FavoriteBorder } from '@mui/icons-material'
import { motion } from 'framer-motion'

const LikeButton = ({ liked = false, count = 0, onLike, disabled = false }) => {
  const [isAnimating, setIsAnimating] = useState(false)

  const handleLike = () => {
    if (disabled || isAnimating) return
    
    setIsAnimating(true)
    onLike?.()
    
    // Reset animation after a short delay
    setTimeout(() => setIsAnimating(false), 300)
  }

  return (
    <Box display="flex" alignItems="center" gap={0.5}>
      <motion.div
        whileTap={{ scale: 0.9 }}
        animate={isAnimating ? { scale: [1, 1.2, 1] } : {}}
        transition={{ duration: 0.3 }}
      >
        <IconButton
          onClick={handleLike}
          disabled={disabled}
          sx={{
            color: liked ? 'error.main' : 'text.secondary',
            '&:hover': {
              color: 'error.main',
              backgroundColor: 'error.main',
              opacity: 0.1
            }
          }}
        >
          {liked ? <Favorite /> : <FavoriteBorder />}
        </IconButton>
      </motion.div>
      
      {count > 0 && (
        <Typography variant="body2" color="text.secondary">
          {count}
        </Typography>
      )}
    </Box>
  )
}

export default LikeButton
