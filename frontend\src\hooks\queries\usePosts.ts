import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query'
import { useSnackbar } from 'notistack'
import postsService from '@/services/postsService'
import { queryKeys } from '@/lib/react-query'
import type { Post, PostForm, PaginatedResponse } from '@/types'

// Get posts with infinite scroll
export const useInfinitePosts = (type: 'all' | 'following' | 'trending' = 'all') => {
  return useInfiniteQuery({
    queryKey: queryKeys.posts.feed(type),
    queryFn: ({ pageParam = 1 }) => {
      const endpoint = type === 'following' ? 'getFollowingPosts' : 
                     type === 'trending' ? 'getTrendingPosts' : 'getPosts'
      return postsService[endpoint]({ page: pageParam, limit: 10 })
    },
    getNextPageParam: (lastPage: PaginatedResponse<Post>) => {
      return lastPage.pagination.hasNext ? lastPage.pagination.page + 1 : undefined
    },
    initialPageParam: 1,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })
}

// Get single post
export const usePost = (postId: string) => {
  return useQuery({
    queryKey: queryKeys.posts.detail(postId),
    queryFn: () => postsService.getPost(postId),
    enabled: !!postId,
  })
}

// Get user posts
export const useUserPosts = (userId: string) => {
  return useQuery({
    queryKey: queryKeys.posts.user(userId),
    queryFn: () => postsService.getUserPosts(userId),
    enabled: !!userId,
  })
}

// Create post mutation
export const useCreatePost = () => {
  const queryClient = useQueryClient()
  const { enqueueSnackbar } = useSnackbar()

  return useMutation({
    mutationFn: (postData: PostForm) => postsService.createPost(postData),
    onSuccess: (newPost) => {
      // Invalidate and refetch posts
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.all })
      
      // Add to cache optimistically
      queryClient.setQueryData(queryKeys.posts.detail(newPost.id), newPost)
      
      enqueueSnackbar('Post created successfully!', { variant: 'success' })
    },
    onError: (error: any) => {
      enqueueSnackbar(error.response?.data?.message || 'Failed to create post', { 
        variant: 'error' 
      })
    },
  })
}

// Update post mutation
export const useUpdatePost = () => {
  const queryClient = useQueryClient()
  const { enqueueSnackbar } = useSnackbar()

  return useMutation({
    mutationFn: ({ postId, data }: { postId: string; data: Partial<PostForm> }) =>
      postsService.updatePost(postId, data),
    onSuccess: (updatedPost, { postId }) => {
      // Update specific post in cache
      queryClient.setQueryData(queryKeys.posts.detail(postId), updatedPost)
      
      // Invalidate posts lists
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.lists() })
      
      enqueueSnackbar('Post updated successfully!', { variant: 'success' })
    },
    onError: (error: any) => {
      enqueueSnackbar(error.response?.data?.message || 'Failed to update post', { 
        variant: 'error' 
      })
    },
  })
}

// Delete post mutation
export const useDeletePost = () => {
  const queryClient = useQueryClient()
  const { enqueueSnackbar } = useSnackbar()

  return useMutation({
    mutationFn: (postId: string) => postsService.deletePost(postId),
    onSuccess: (_, postId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: queryKeys.posts.detail(postId) })
      
      // Invalidate posts lists
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.lists() })
      
      enqueueSnackbar('Post deleted successfully!', { variant: 'success' })
    },
    onError: (error: any) => {
      enqueueSnackbar(error.response?.data?.message || 'Failed to delete post', { 
        variant: 'error' 
      })
    },
  })
}

// Like post mutation
export const useLikePost = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (postId: string) => postsService.likePost(postId),
    onMutate: async (postId) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.posts.detail(postId) })

      // Snapshot previous value
      const previousPost = queryClient.getQueryData<Post>(queryKeys.posts.detail(postId))

      // Optimistically update
      if (previousPost) {
        queryClient.setQueryData<Post>(queryKeys.posts.detail(postId), {
          ...previousPost,
          isLiked: true,
          likesCount: previousPost.likesCount + 1,
        })
      }

      return { previousPost }
    },
    onError: (error, postId, context) => {
      // Rollback on error
      if (context?.previousPost) {
        queryClient.setQueryData(queryKeys.posts.detail(postId), context.previousPost)
      }
    },
    onSettled: (_, __, postId) => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.detail(postId) })
    },
  })
}

// Unlike post mutation
export const useUnlikePost = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (postId: string) => postsService.unlikePost(postId),
    onMutate: async (postId) => {
      await queryClient.cancelQueries({ queryKey: queryKeys.posts.detail(postId) })

      const previousPost = queryClient.getQueryData<Post>(queryKeys.posts.detail(postId))

      if (previousPost) {
        queryClient.setQueryData<Post>(queryKeys.posts.detail(postId), {
          ...previousPost,
          isLiked: false,
          likesCount: Math.max(0, previousPost.likesCount - 1),
        })
      }

      return { previousPost }
    },
    onError: (error, postId, context) => {
      if (context?.previousPost) {
        queryClient.setQueryData(queryKeys.posts.detail(postId), context.previousPost)
      }
    },
    onSettled: (_, __, postId) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.detail(postId) })
    },
  })
}

// Save post mutation
export const useSavePost = () => {
  const queryClient = useQueryClient()
  const { enqueueSnackbar } = useSnackbar()

  return useMutation({
    mutationFn: (postId: string) => postsService.toggleSavePost(postId),
    onSuccess: (_, postId) => {
      queryClient.invalidateQueries({ queryKey: queryKeys.posts.detail(postId) })
      enqueueSnackbar('Post saved!', { variant: 'success' })
    },
    onError: (error: any) => {
      enqueueSnackbar(error.response?.data?.message || 'Failed to save post', { 
        variant: 'error' 
      })
    },
  })
}

// Search posts
export const useSearchPosts = (query: string) => {
  return useQuery({
    queryKey: queryKeys.posts.search(query),
    queryFn: () => postsService.searchPosts(query),
    enabled: !!query && query.length > 2,
    staleTime: 1000 * 60 * 2, // 2 minutes
  })
}
