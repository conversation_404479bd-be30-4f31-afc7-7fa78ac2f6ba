import { useState, useEffect } from 'react'
import {
  Box,
  TextField,
  Button,
  Typography,
  Avatar,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  IconButton,
  Divider
} from '@mui/material'
import { Send, MoreVert } from '@mui/icons-material'
import { useAuth } from '../../context/AuthContext'
import { useSnackbar } from 'notistack'
import postsService from '../../services/postsService'
import { formatDate } from '../../utils'

const CommentSection = ({ postId, onComment }) => {
  const [comments, setComments] = useState([])
  const [newComment, setNewComment] = useState('')
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)
  const { user } = useAuth()
  const { enqueueSnackbar } = useSnackbar()

  useEffect(() => {
    fetchComments()
  }, [postId])

  const fetchComments = async () => {
    try {
      setLoading(true)
      const commentsData = await postsService.getComments(postId)
      setComments(commentsData)
    } catch (error) {
      console.error('Error fetching comments:', error)
      enqueueSnackbar('Failed to load comments', { variant: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleSubmitComment = async () => {
    if (!newComment.trim() || submitting) return

    try {
      setSubmitting(true)
      const comment = await postsService.addComment(postId, newComment.trim())
      setComments(prev => [comment, ...prev])
      setNewComment('')
      onComment?.()
      enqueueSnackbar('Comment added!', { variant: 'success' })
    } catch (error) {
      console.error('Error adding comment:', error)
      enqueueSnackbar('Failed to add comment', { variant: 'error' })
    } finally {
      setSubmitting(false)
    }
  }

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      handleSubmitComment()
    }
  }

  return (
    <Box sx={{ p: 2 }}>
      {/* Add Comment */}
      <Box display="flex" gap={2} mb={2}>
        <Avatar src={user?.avatar} sx={{ width: 32, height: 32 }}>
          {user?.name?.charAt(0)?.toUpperCase()}
        </Avatar>
        <Box flex={1}>
          <TextField
            fullWidth
            multiline
            maxRows={3}
            placeholder="Write a comment..."
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            onKeyPress={handleKeyPress}
            variant="outlined"
            size="small"
            sx={{ mb: 1 }}
          />
          <Box display="flex" justifyContent="flex-end">
            <Button
              variant="contained"
              size="small"
              onClick={handleSubmitComment}
              disabled={!newComment.trim() || submitting}
              startIcon={<Send />}
            >
              {submitting ? 'Posting...' : 'Comment'}
            </Button>
          </Box>
        </Box>
      </Box>

      <Divider sx={{ mb: 2 }} />

      {/* Comments List */}
      {loading ? (
        <Typography variant="body2" color="text.secondary" textAlign="center">
          Loading comments...
        </Typography>
      ) : comments.length === 0 ? (
        <Typography variant="body2" color="text.secondary" textAlign="center">
          No comments yet. Be the first to comment!
        </Typography>
      ) : (
        <List disablePadding>
          {comments.map((comment, index) => (
            <ListItem key={comment.id || index} alignItems="flex-start" disablePadding>
              <ListItemAvatar>
                <Avatar 
                  src={comment.author?.avatar} 
                  sx={{ width: 32, height: 32 }}
                >
                  {comment.author?.name?.charAt(0)?.toUpperCase()}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="subtitle2" fontWeight={600}>
                      {comment.author?.name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatDate(comment.createdAt)}
                    </Typography>
                  </Box>
                }
                secondary={
                  <Typography variant="body2" sx={{ mt: 0.5 }}>
                    {comment.content}
                  </Typography>
                }
              />
              <IconButton size="small">
                <MoreVert fontSize="small" />
              </IconButton>
            </ListItem>
          ))}
        </List>
      )}
    </Box>
  )
}

export default CommentSection
