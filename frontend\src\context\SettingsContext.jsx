import React, { createContext, useContext, useState, useEffect } from 'react'
import { useSnackbar } from 'notistack'
import axios from '../utils/fixedAxios'

const SettingsContext = createContext()

export const useSettings = () => {
  const context = useContext(SettingsContext)
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}

export const SettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState(null)
  const { enqueueSnackbar } = useSnackbar()

  // Default settings structure
  const defaultSettings = {
    profile: {
      name: '',
      username: '',
      email: '',
      bio: '',
      location: '',
      website: '',
      profilePicture: '',
      isPrivate: false
    },
    notifications: {
      email: true,
      push: true,
      inApp: true,
      sms: false,
      likes: true,
      comments: true,
      follows: true,
      messages: true,
      liveStreams: true,
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00',
        timezone: 'UTC'
      }
    },
    privacy: {
      profileVisibility: 'public',
      showEmail: false,
      showPhone: false,
      allowMessages: 'everyone',
      allowTagging: true,
      allowMentions: true,
      showOnlineStatus: true,
      allowLocationTracking: false
    },
    preferences: {
      language: 'en',
      timezone: 'UTC',
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      currency: 'USD',
      autoPlayVideos: true,
      showSensitiveContent: false
    },
    theme: {
      mode: 'light',
      primaryColor: '#1976d2',
      accentColor: '#e91e63'
    },
    wellness: {
      contentFilters: {
        hideNegativeContent: false,
        preferredEmotions: [],
        avoidedEmotions: []
      },
      timeLimit: {
        enabled: false,
        dailyLimitMinutes: 60
      },
      hideMetrics: false,
      wellnessReminders: {
        enabled: true,
        frequency: 'every2hours'
      }
    },
    security: {
      twoFactorEnabled: false,
      loginAlerts: true,
      sessionTimeout: 30,
      allowedDevices: 'unlimited'
    }
  }

  // Load settings from backend
  const loadSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      // Check if user is authenticated
      const token = localStorage.getItem('token')
      if (!token) {
        console.warn('No authentication token found')
        setSettings(defaultSettings)
        setLoading(false)
        return
      }

      console.log('Loading settings with token:', token.substring(0, 20) + '...')
      const response = await axios.get('/api/users/settings')

      if (response.data.success) {
        console.log('Settings loaded successfully:', response.data.data)
        setSettings(response.data.data)
      } else {
        throw new Error('Failed to load settings')
      }
    } catch (err) {
      console.error('Error loading settings:', err)
      console.error('Error response:', err.response?.data)
      console.error('Error status:', err.response?.status)

      // Handle specific error cases
      if (err.response?.status === 401) {
        setError('Authentication failed. Please log in again.')
        enqueueSnackbar('Authentication failed. Please log in again.', { variant: 'error' })
        // Redirect to login or trigger re-authentication
        localStorage.removeItem('token')
        window.location.href = '/auth'
        return
      } else if (err.response?.status === 403) {
        setError('Access denied. You do not have permission to access settings.')
        enqueueSnackbar('Access denied. Please check your permissions.', { variant: 'error' })
      } else {
        setError(err.response?.data?.message || err.message)
        enqueueSnackbar('Failed to load settings, using defaults', { variant: 'warning' })
      }

      // Use default settings if loading fails
      setSettings(defaultSettings)
    } finally {
      setLoading(false)
    }
  }

  // Save settings to backend
  const saveSettings = async (updatedSettings) => {
    try {
      setSaving(true)
      setError(null)

      // Check if user is authenticated
      const token = localStorage.getItem('token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      console.log('Saving settings:', updatedSettings)
      const response = await axios.put('/api/users/settings', updatedSettings)

      if (response.data.success) {
        setSettings(prev => ({ ...prev, ...updatedSettings }))
        enqueueSnackbar('Settings saved successfully', { variant: 'success' })
        return true
      } else {
        throw new Error('Failed to save settings')
      }
    } catch (err) {
      console.error('Error saving settings:', err)
      console.error('Error response:', err.response?.data)
      console.error('Error status:', err.response?.status)

      // Handle specific error cases
      if (err.response?.status === 401) {
        setError('Authentication failed. Please log in again.')
        enqueueSnackbar('Authentication failed. Please log in again.', { variant: 'error' })
        localStorage.removeItem('token')
        window.location.href = '/auth'
      } else if (err.response?.status === 403) {
        setError('Access denied. You do not have permission to save settings.')
        enqueueSnackbar('Access denied. Please check your permissions.', { variant: 'error' })
      } else {
        setError(err.response?.data?.message || err.message)
        enqueueSnackbar(err.response?.data?.message || 'Failed to save settings', {
          variant: 'error'
        })
      }
      return false
    } finally {
      setSaving(false)
    }
  }

  // Update specific setting section
  const updateSettings = async (section, data) => {
    const updatedSettings = {
      [section]: {
        ...settings[section],
        ...data
      }
    }
    return await saveSettings(updatedSettings)
  }

  // Update password
  const updatePassword = async (currentPassword, newPassword) => {
    try {
      setSaving(true)
      setError(null)

      const response = await axios.put('/api/users/settings/password', {
        currentPassword,
        newPassword
      })

      if (response.data.success) {
        enqueueSnackbar('Password updated successfully', { variant: 'success' })
        return true
      } else {
        throw new Error('Failed to update password')
      }
    } catch (err) {
      console.error('Error updating password:', err)
      setError(err.message)
      enqueueSnackbar(err.response?.data?.message || 'Failed to update password', {
        variant: 'error'
      })
      return false
    } finally {
      setSaving(false)
    }
  }

  // Delete account
  const deleteAccount = async () => {
    try {
      setSaving(true)
      setError(null)

      const response = await axios.delete('/api/users/settings/account')

      if (response.data.success) {
        enqueueSnackbar('Account deleted successfully', { variant: 'success' })
        // Clear local storage and redirect
        localStorage.removeItem('token')
        window.location.href = '/login'
        return true
      } else {
        throw new Error('Failed to delete account')
      }
    } catch (err) {
      console.error('Error deleting account:', err)
      setError(err.message)
      enqueueSnackbar(err.response?.data?.message || 'Failed to delete account', {
        variant: 'error'
      })
      return false
    } finally {
      setSaving(false)
    }
  }

  // Export user data
  const exportData = async () => {
    try {
      const response = await axios.get('/api/users/settings/export-data', {
        responseType: 'blob'
      })

      // Create download link
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', `user_data_${new Date().toISOString().split('T')[0]}.json`)
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      enqueueSnackbar('Data exported successfully', { variant: 'success' })
      return true
    } catch (err) {
      console.error('Error exporting data:', err)
      enqueueSnackbar('Failed to export data', { variant: 'error' })
      return false
    }
  }

  // Load settings on mount
  useEffect(() => {
    loadSettings()
  }, [])

  const value = {
    settings: settings || defaultSettings,
    loading,
    saving,
    error,
    loadSettings,
    saveSettings,
    updateSettings,
    updatePassword,
    deleteAccount,
    exportData
  }

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  )
}

export default SettingsProvider
