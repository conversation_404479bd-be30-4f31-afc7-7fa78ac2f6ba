import React from 'react'
import { motion } from 'framer-motion'
import { Box, useTheme } from '@mui/material'

const AuthBackground = () => {
  const theme = useTheme()

  const floatingShapes = Array.from({ length: 6 }, (_, i) => ({
    id: i,
    size: Math.random() * 100 + 50,
    x: Math.random() * 100,
    y: Math.random() * 100,
    duration: Math.random() * 20 + 10,
    delay: Math.random() * 5
  }))

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        overflow: 'hidden',
        zIndex: 1
      }}
    >
      {/* Animated gradient orbs */}
      {floatingShapes.map((shape) => (
        <motion.div
          key={shape.id}
          initial={{
            x: `${shape.x}vw`,
            y: `${shape.y}vh`,
            scale: 0,
            opacity: 0
          }}
          animate={{
            x: [`${shape.x}vw`, `${(shape.x + 20) % 100}vw`, `${shape.x}vw`],
            y: [`${shape.y}vh`, `${(shape.y + 30) % 100}vh`, `${shape.y}vh`],
            scale: [0, 1, 0.8, 1],
            opacity: [0, 0.6, 0.3, 0.6]
          }}
          transition={{
            duration: shape.duration,
            repeat: Infinity,
            delay: shape.delay,
            ease: "easeInOut"
          }}
          style={{
            position: 'absolute',
            width: shape.size,
            height: shape.size,
            borderRadius: '50%',
            background: theme.palette.mode === 'dark'
              ? `radial-gradient(circle, rgba(102, 126, 234, 0.4) 0%, rgba(118, 75, 162, 0.2) 50%, transparent 70%)`
              : `radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.1) 50%, transparent 70%)`,
            filter: 'blur(1px)',
            pointerEvents: 'none'
          }}
        />
      ))}

      {/* Geometric patterns */}
      <motion.div
        initial={{ opacity: 0, rotate: 0 }}
        animate={{ opacity: 0.1, rotate: 360 }}
        transition={{ duration: 50, repeat: Infinity, ease: "linear" }}
        style={{
          position: 'absolute',
          top: '10%',
          right: '10%',
          width: '200px',
          height: '200px',
          border: `2px solid ${theme.palette.primary.main}`,
          borderRadius: '50%',
          pointerEvents: 'none'
        }}
      />

      <motion.div
        initial={{ opacity: 0, rotate: 0 }}
        animate={{ opacity: 0.1, rotate: -360 }}
        transition={{ duration: 40, repeat: Infinity, ease: "linear" }}
        style={{
          position: 'absolute',
          bottom: '20%',
          left: '15%',
          width: '150px',
          height: '150px',
          border: `2px solid ${theme.palette.secondary.main}`,
          transform: 'rotate(45deg)',
          pointerEvents: 'none'
        }}
      />

      {/* Particle effect */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: theme.palette.mode === 'dark'
            ? `radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
               radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
               radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%)`
            : `radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.2) 0%, transparent 50%),
               radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.2) 0%, transparent 50%),
               radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%)`,
          pointerEvents: 'none'
        }}
      />
    </Box>
  )
}

export default AuthBackground
