import { useState } from 'react'
import {
  Card,
  CardContent,
  CardActions,
  CardMedia,
  Avatar,
  Typography,
  IconButton,
  Box,
  Chip,
  Menu,
  MenuItem,
  Divider
} from '@mui/material'
import {
  Favorite,
  FavoriteBorder,
  Comment,
  Share,
  BookmarkBorder,
  Bookmark,
  MoreVert,
  Edit,
  Delete,
  Report
} from '@mui/icons-material'
import { formatDate } from '@/utils'
import type { Post } from '@/types'

interface PostCardProps {
  post: Post
  onLike?: () => void
  onComment?: () => void
  onShare?: () => void
  onSave?: () => void
  onEdit?: () => void
  onDelete?: () => void
  onReport?: () => void
  showComments?: boolean
}

const PostCard = ({
  post,
  onLike,
  onComment,
  onShare,
  onSave,
  onEdit,
  onDelete,
  onReport,
  showComments = false
}: PostCardProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const open = Boolean(anchorEl)

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleMenuClose = () => {
    setAnchorEl(null)
  }

  const handleMenuAction = (action: () => void | undefined) => {
    action?.()
    handleMenuClose()
  }

  return (
    <Card sx={{ maxWidth: '100%', mb: 2, borderRadius: 2, boxShadow: 2 }}>
      {/* Post Header */}
      <CardContent sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box display="flex" alignItems="center">
            <Avatar
              src={post.author?.avatar}
              sx={{ bgcolor: 'primary.main', mr: 2, width: 48, height: 48 }}
            >
              {post.author?.name?.charAt(0)?.toUpperCase()}
            </Avatar>
            <Box>
              <Typography variant="subtitle1" fontWeight={600}>
                {post.author?.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                @{post.author?.username} • {formatDate(post.createdAt)}
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" alignItems="center" gap={1}>
            {post.mood && (
              <Chip
                label={`${post.mood.emoji} ${post.mood.label}`}
                size="small"
                variant="outlined"
                sx={{ 
                  borderColor: post.mood.color,
                  color: post.mood.color
                }}
              />
            )}
            
            <IconButton onClick={handleMenuClick} size="small">
              <MoreVert />
            </IconButton>
          </Box>
        </Box>
      </CardContent>

      {/* Post Content */}
      {post.content && (
        <CardContent sx={{ pt: 0, pb: 1 }}>
          <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
            {post.content}
          </Typography>
          
          {post.tags && post.tags.length > 0 && (
            <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {post.tags.map((tag, index) => (
                <Typography
                  key={index}
                  variant="body2"
                  color="primary"
                  sx={{ cursor: 'pointer', '&:hover': { textDecoration: 'underline' } }}
                >
                  #{tag}
                </Typography>
              ))}
            </Box>
          )}
        </CardContent>
      )}

      {/* Post Media */}
      {post.media && post.media.length > 0 && (
        <CardMedia
          component="img"
          image={post.media[0].url}
          alt="Post media"
          sx={{ 
            maxHeight: 500, 
            objectFit: 'cover',
            cursor: 'pointer'
          }}
        />
      )}

      {/* Post Actions */}
      <CardActions sx={{ justifyContent: 'space-between', px: 2, py: 1 }}>
        <Box display="flex" alignItems="center" gap={1}>
          <IconButton
            onClick={onLike}
            color={post.isLiked ? 'error' : 'default'}
            size="small"
          >
            {post.isLiked ? <Favorite /> : <FavoriteBorder />}
          </IconButton>
          <Typography variant="body2" color="text.secondary">
            {post.likesCount}
          </Typography>

          <IconButton onClick={onComment} size="small">
            <Comment />
          </IconButton>
          <Typography variant="body2" color="text.secondary">
            {post.commentsCount}
          </Typography>

          <IconButton onClick={onShare} size="small">
            <Share />
          </IconButton>
          <Typography variant="body2" color="text.secondary">
            {post.sharesCount}
          </Typography>
        </Box>

        <IconButton
          onClick={onSave}
          color={post.isSaved ? 'primary' : 'default'}
          size="small"
        >
          {post.isSaved ? <Bookmark /> : <BookmarkBorder />}
        </IconButton>
      </CardActions>

      {/* Comments Section */}
      {showComments && (
        <>
          <Divider />
          <CardContent>
            <Typography variant="body2" color="text.secondary">
              Comments section would go here
            </Typography>
          </CardContent>
        </>
      )}

      {/* More Menu */}
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {onEdit && (
          <MenuItem onClick={() => handleMenuAction(onEdit)}>
            <Edit sx={{ mr: 1 }} fontSize="small" />
            Edit
          </MenuItem>
        )}
        {onDelete && (
          <MenuItem onClick={() => handleMenuAction(onDelete)}>
            <Delete sx={{ mr: 1 }} fontSize="small" />
            Delete
          </MenuItem>
        )}
        {onReport && (
          <MenuItem onClick={() => handleMenuAction(onReport)}>
            <Report sx={{ mr: 1 }} fontSize="small" />
            Report
          </MenuItem>
        )}
      </Menu>
    </Card>
  )
}

export default PostCard
