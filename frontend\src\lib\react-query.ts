import { QueryClient, DefaultOptions } from '@tanstack/react-query'
import { AxiosError } from 'axios'

const queryConfig: DefaultOptions = {
  queries: {
    // Global query options
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes (formerly cacheTime)
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors except 408, 429
      if (error instanceof AxiosError) {
        const status = error.response?.status
        if (status && status >= 400 && status < 500 && status !== 408 && status !== 429) {
          return false
        }
      }
      return failureCount < 3
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false,
    refetchOnReconnect: 'always',
  },
  mutations: {
    // Global mutation options
    retry: (failureCount, error) => {
      // Don't retry mutations on client errors
      if (error instanceof AxiosError) {
        const status = error.response?.status
        if (status && status >= 400 && status < 500) {
          return false
        }
      }
      return failureCount < 2
    },
  },
}

export const queryClient = new QueryClient({
  defaultOptions: queryConfig,
})

// Query Keys Factory
export const queryKeys = {
  // Auth
  auth: {
    me: ['auth', 'me'] as const,
    permissions: ['auth', 'permissions'] as const,
  },
  
  // Posts
  posts: {
    all: ['posts'] as const,
    lists: () => [...queryKeys.posts.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.posts.lists(), filters] as const,
    details: () => [...queryKeys.posts.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.posts.details(), id] as const,
    feed: (type: string) => [...queryKeys.posts.all, 'feed', type] as const,
    user: (userId: string) => [...queryKeys.posts.all, 'user', userId] as const,
    search: (query: string) => [...queryKeys.posts.all, 'search', query] as const,
  },
  
  // Users
  users: {
    all: ['users'] as const,
    lists: () => [...queryKeys.users.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.users.lists(), filters] as const,
    details: () => [...queryKeys.users.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.users.details(), id] as const,
    search: (query: string) => [...queryKeys.users.all, 'search', query] as const,
    suggested: ['users', 'suggested'] as const,
    followers: (userId: string) => [...queryKeys.users.all, 'followers', userId] as const,
    following: (userId: string) => [...queryKeys.users.all, 'following', userId] as const,
  },
  
  // Comments
  comments: {
    all: ['comments'] as const,
    post: (postId: string) => [...queryKeys.comments.all, 'post', postId] as const,
  },
  
  // Notifications
  notifications: {
    all: ['notifications'] as const,
    unread: ['notifications', 'unread'] as const,
  },
  
  // Messages
  messages: {
    all: ['messages'] as const,
    conversations: ['messages', 'conversations'] as const,
    conversation: (id: string) => [...queryKeys.messages.all, 'conversation', id] as const,
  },
  
  // Stories
  stories: {
    all: ['stories'] as const,
    user: (userId: string) => [...queryKeys.stories.all, 'user', userId] as const,
  },
  
  // Reels
  reels: {
    all: ['reels'] as const,
    trending: ['reels', 'trending'] as const,
    user: (userId: string) => [...queryKeys.reels.all, 'user', userId] as const,
  },
  
  // Shop
  shop: {
    all: ['shop'] as const,
    products: ['shop', 'products'] as const,
    product: (id: string) => [...queryKeys.shop.products, id] as const,
    categories: ['shop', 'categories'] as const,
    cart: ['shop', 'cart'] as const,
    orders: ['shop', 'orders'] as const,
  },
  
  // Live Streams
  streams: {
    all: ['streams'] as const,
    live: ['streams', 'live'] as const,
    user: (userId: string) => [...queryKeys.streams.all, 'user', userId] as const,
  },
} as const

// Utility functions for cache management
export const invalidateQueries = {
  posts: () => queryClient.invalidateQueries({ queryKey: queryKeys.posts.all }),
  users: () => queryClient.invalidateQueries({ queryKey: queryKeys.users.all }),
  notifications: () => queryClient.invalidateQueries({ queryKey: queryKeys.notifications.all }),
  messages: () => queryClient.invalidateQueries({ queryKey: queryKeys.messages.all }),
  all: () => queryClient.invalidateQueries(),
}

export const removeQueries = {
  posts: () => queryClient.removeQueries({ queryKey: queryKeys.posts.all }),
  users: () => queryClient.removeQueries({ queryKey: queryKeys.users.all }),
  all: () => queryClient.removeQueries(),
}

// Prefetch utilities
export const prefetchQueries = {
  userPosts: async (userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.posts.user(userId),
      queryFn: () => import('@/services/postsService').then(m => m.default.getUserPosts(userId)),
      staleTime: 1000 * 60 * 5,
    })
  },
  
  userProfile: async (userId: string) => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.users.detail(userId),
      queryFn: () => import('@/services/usersService').then(m => m.default.getUser(userId)),
      staleTime: 1000 * 60 * 5,
    })
  },
}

export default queryClient
