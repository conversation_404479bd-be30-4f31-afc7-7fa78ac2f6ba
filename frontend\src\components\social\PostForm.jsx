import { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Avatar,
  Typography,
  IconButton,
  Chip,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material'
import {
  Close,
  Image,
  Mood,
  LocationOn,
  Poll,
  Public,
  People,
  Lock
} from '@mui/icons-material'
import { useAuth } from '../../context/AuthContext'

const PostForm = ({
  open = false,
  onClose,
  onSubmit,
  compact = false,
  placeholder = "What's happening?",
  fullScreen = false
}) => {
  const { user } = useAuth()
  const [content, setContent] = useState('')
  const [selectedMood, setSelectedMood] = useState('')
  const [privacy, setPrivacy] = useState('public')
  const [loading, setLoading] = useState(false)

  const moods = [
    { value: 'happy', label: 'Happy', emoji: '😊' },
    { value: 'excited', label: 'Excited', emoji: '🎉' },
    { value: 'grateful', label: 'Grateful', emoji: '🙏' },
    { value: 'thoughtful', label: 'Thoughtful', emoji: '🤔' },
    { value: 'relaxed', label: 'Relaxed', emoji: '😌' },
    { value: 'motivated', label: 'Motivated', emoji: '💪' }
  ]

  const privacyOptions = [
    { value: 'public', label: 'Public', icon: Public },
    { value: 'friends', label: 'Friends', icon: People },
    { value: 'private', label: 'Only me', icon: Lock }
  ]

  const handleSubmit = async () => {
    if (!content.trim()) return

    setLoading(true)
    try {
      const postData = {
        content: content.trim(),
        privacy,
        mood: selectedMood || undefined
      }

      await onSubmit(postData)
      setContent('')
      setSelectedMood('')
      setPrivacy('public')
      if (onClose) onClose()
    } catch (error) {
      console.error('Error creating post:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      handleSubmit()
    }
  }

  // Compact version for sidebar
  if (compact) {
    return (
      <Paper
        elevation={1}
        sx={{
          p: 2,
          borderRadius: 2,
          border: '1px solid',
          borderColor: 'divider'
        }}
      >
        <Box display="flex" gap={2} mb={2}>
          <Avatar src={user?.avatar} sx={{ width: 40, height: 40 }}>
            {user?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <TextField
            fullWidth
            multiline
            rows={2}
            placeholder={placeholder}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyPress={handleKeyPress}
            variant="outlined"
            size="small"
          />
        </Box>
        
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" gap={1}>
            <IconButton size="small" color="primary">
              <Image />
            </IconButton>
            <IconButton size="small" color="primary">
              <Mood />
            </IconButton>
          </Box>
          
          <Button
            variant="contained"
            size="small"
            onClick={handleSubmit}
            disabled={!content.trim() || loading}
          >
            {loading ? 'Posting...' : 'Post'}
          </Button>
        </Box>
      </Paper>
    )
  }

  // Full dialog version
  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: fullScreen ? 0 : 2,
          minHeight: fullScreen ? '100vh' : 'auto'
        }
      }}
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6" fontWeight={600}>
            Create Post
          </Typography>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box display="flex" gap={2} mb={2}>
          <Avatar src={user?.avatar} sx={{ width: 48, height: 48 }}>
            {user?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <Box>
            <Typography variant="subtitle1" fontWeight={600}>
              {user?.name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              @{user?.username}
            </Typography>
          </Box>
        </Box>

        <TextField
          fullWidth
          multiline
          rows={4}
          placeholder={placeholder}
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onKeyPress={handleKeyPress}
          variant="outlined"
          sx={{ mb: 2 }}
        />

        {selectedMood && (
          <Box mb={2}>
            <Chip
              label={`Feeling ${moods.find(m => m.value === selectedMood)?.label}`}
              onDelete={() => setSelectedMood('')}
              color="primary"
              variant="outlined"
              avatar={<span>{moods.find(m => m.value === selectedMood)?.emoji}</span>}
            />
          </Box>
        )}

        <Box display="flex" gap={2} mb={2}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Mood</InputLabel>
            <Select
              value={selectedMood}
              onChange={(e) => setSelectedMood(e.target.value)}
              label="Mood"
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {moods.map((mood) => (
                <MenuItem key={mood.value} value={mood.value}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <span>{mood.emoji}</span>
                    {mood.label}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Privacy</InputLabel>
            <Select
              value={privacy}
              onChange={(e) => setPrivacy(e.target.value)}
              label="Privacy"
            >
              {privacyOptions.map((option) => {
                const IconComponent = option.icon
                return (
                  <MenuItem key={option.value} value={option.value}>
                    <Box display="flex" alignItems="center" gap={1}>
                      <IconComponent fontSize="small" />
                      {option.label}
                    </Box>
                  </MenuItem>
                )
              })}
            </Select>
          </FormControl>
        </Box>

        <Box display="flex" gap={1} mb={2}>
          <IconButton color="primary">
            <Image />
          </IconButton>
          <IconButton color="primary">
            <LocationOn />
          </IconButton>
          <IconButton color="primary">
            <Poll />
          </IconButton>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!content.trim() || loading}
        >
          {loading ? 'Posting...' : 'Post'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default PostForm
