import React, { useState, useRef } from 'react'
import {
  Box,
  Button,
  Typography,
  IconButton,
  LinearProgress,
  Paper,
  Avatar
} from '@mui/material'
import {
  CloudUpload,
  Delete,
  PhotoCamera,
  Image as ImageIcon
} from '@mui/icons-material'
import { useSnackbar } from 'notistack'

const ImageUpload = ({
  onUpload,
  onRemove,
  value,
  multiple = false,
  maxSize = 5 * 1024 * 1024, // 5MB
  acceptedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  variant = 'default', // default, avatar, banner
  width = 200,
  height = 200,
  loading = false
}) => {
  const [dragOver, setDragOver] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const fileInputRef = useRef(null)
  const { enqueueSnackbar } = useSnackbar()

  const handleFileSelect = (files) => {
    const fileArray = Array.from(files)
    
    // Validate files
    const validFiles = fileArray.filter(file => {
      if (!acceptedTypes.includes(file.type)) {
        enqueueSnackbar(`${file.name} is not a supported image format`, { variant: 'error' })
        return false
      }
      
      if (file.size > maxSize) {
        enqueueSnackbar(`${file.name} is too large. Maximum size is ${maxSize / 1024 / 1024}MB`, { variant: 'error' })
        return false
      }
      
      return true
    })

    if (validFiles.length > 0) {
      onUpload(multiple ? validFiles : validFiles[0])
    }
  }

  const handleDrop = (e) => {
    e.preventDefault()
    setDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const handleDragOver = (e) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileInputChange = (e) => {
    if (e.target.files) {
      handleFileSelect(e.target.files)
    }
  }

  const renderUploadArea = () => {
    if (variant === 'avatar') {
      return (
        <Box position="relative" display="inline-block">
          <Avatar
            src={value}
            sx={{ 
              width, 
              height,
              cursor: 'pointer',
              border: dragOver ? '2px dashed' : '2px solid transparent',
              borderColor: dragOver ? 'primary.main' : 'transparent'
            }}
            onClick={handleClick}
          >
            {!value && <PhotoCamera />}
          </Avatar>
          {value && (
            <IconButton
              size="small"
              sx={{
                position: 'absolute',
                top: -8,
                right: -8,
                bgcolor: 'error.main',
                color: 'white',
                '&:hover': { bgcolor: 'error.dark' }
              }}
              onClick={(e) => {
                e.stopPropagation()
                onRemove?.()
              }}
            >
              <Delete fontSize="small" />
            </IconButton>
          )}
        </Box>
      )
    }

    return (
      <Paper
        elevation={dragOver ? 4 : 1}
        sx={{
          width,
          height,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          border: dragOver ? '2px dashed' : '2px solid transparent',
          borderColor: dragOver ? 'primary.main' : 'grey.300',
          bgcolor: dragOver ? 'action.hover' : 'background.paper',
          transition: 'all 0.2s ease',
          p: 2
        }}
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {value ? (
          <Box position="relative" width="100%" height="100%">
            <img
              src={typeof value === 'string' ? value : URL.createObjectURL(value)}
              alt="Upload preview"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'cover',
                borderRadius: 4
              }}
            />
            <IconButton
              size="small"
              sx={{
                position: 'absolute',
                top: 8,
                right: 8,
                bgcolor: 'rgba(0,0,0,0.5)',
                color: 'white',
                '&:hover': { bgcolor: 'rgba(0,0,0,0.7)' }
              }}
              onClick={(e) => {
                e.stopPropagation()
                onRemove?.()
              }}
            >
              <Delete fontSize="small" />
            </IconButton>
          </Box>
        ) : (
          <>
            <ImageIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
            <Typography variant="body2" color="text.secondary" textAlign="center">
              {dragOver ? 'Drop image here' : 'Click or drag image here'}
            </Typography>
            <Typography variant="caption" color="text.disabled" textAlign="center" mt={1}>
              Max size: {maxSize / 1024 / 1024}MB
            </Typography>
          </>
        )}
      </Paper>
    )
  }

  return (
    <Box>
      {renderUploadArea()}
      
      {loading && (
        <Box mt={1}>
          <LinearProgress 
            variant={uploadProgress > 0 ? "determinate" : "indeterminate"}
            value={uploadProgress}
          />
        </Box>
      )}
      
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(',')}
        multiple={multiple}
        onChange={handleFileInputChange}
        style={{ display: 'none' }}
      />
    </Box>
  )
}

export default ImageUpload
