import { useLocation, useNavigate } from 'react-router-dom'
import {
  BottomNavigation,
  BottomNavigationAction,
  Paper,
  Badge
} from '@mui/material'
import {
  Home,
  Message,
  Add,
  Person,
  Notifications
} from '@mui/icons-material'

const MobileNavigation = () => {
  const location = useLocation()
  const navigate = useNavigate()

  const navigationItems = [
    { label: 'Home', icon: Home, path: '/' },
    { label: 'Messages', icon: Message, path: '/messages', badge: 3 },
    { label: 'Create', icon: Add, path: '/create' },
    { label: 'Notifications', icon: Notifications, path: '/notifications', badge: 5 },
    { label: 'Profile', icon: Person, path: '/profile' },
  ]

  const getCurrentValue = () => {
    const currentPath = location.pathname
    const index = navigationItems.findIndex(item => 
      item.path === currentPath || 
      (item.path !== '/' && currentPath.startsWith(item.path))
    )
    return index >= 0 ? index : 0
  }

  const handleChange = (event, newValue) => {
    navigate(navigationItems[newValue].path)
  }

  return (
    <Paper
      sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        borderTop: 1,
        borderColor: 'divider'
      }}
      elevation={3}
    >
      <BottomNavigation
        value={getCurrentValue()}
        onChange={handleChange}
        showLabels
      >
        {navigationItems.map((item, index) => {
          const Icon = item.icon
          return (
            <BottomNavigationAction
              key={index}
              label={item.label}
              icon={
                item.badge ? (
                  <Badge badgeContent={item.badge} color="error">
                    <Icon />
                  </Badge>
                ) : (
                  <Icon />
                )
              }
            />
          )
        })}
      </BottomNavigation>
    </Paper>
  )
}

export default MobileNavigation
