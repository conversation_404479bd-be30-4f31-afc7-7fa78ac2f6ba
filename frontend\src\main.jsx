import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App.jsx'
import './index.css'

// Suppress browser extension polyfill errors
window.addEventListener('error', (event) => {
  if (event.filename && event.filename.includes('polyfill.js')) {
    event.preventDefault()
    return false
  }
})

window.addEventListener('unhandledrejection', (event) => {
  if (event.reason && event.reason.message &&
      event.reason.message.includes('Could not establish connection')) {
    event.preventDefault()
    return false
  }
})

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
