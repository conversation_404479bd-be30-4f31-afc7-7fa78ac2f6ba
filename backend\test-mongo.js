const mongoose = require('mongoose');
require('dotenv').config();

console.log('Testing MongoDB connection...');
console.log('MONGO_URI:', process.env.MONGO_URI);

mongoose.connect(process.env.MONGO_URI)
  .then(() => {
    console.log('✅ MongoDB connected successfully');
    process.exit(0);
  })
  .catch(err => {
    console.error('❌ MongoDB connection failed:', err.message);
    console.log('\nPossible solutions:');
    console.log('1. Make sure MongoDB is installed and running');
    console.log('2. Start MongoDB with: mongod');
    console.log('3. Check if MongoDB service is running');
    process.exit(1);
  });
