import React from 'react'
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box
} from '@mui/material'
import { Warning, Info, Error, CheckCircle } from '@mui/icons-material'

const ConfirmDialog = ({
  open,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  type = 'warning', // warning, info, error, success
  loading = false
}) => {
  const getIcon = () => {
    const iconProps = { sx: { fontSize: 48, mb: 2 } }
    
    switch (type) {
      case 'error':
        return <Error color="error" {...iconProps} />
      case 'success':
        return <CheckCircle color="success" {...iconProps} />
      case 'info':
        return <Info color="info" {...iconProps} />
      default:
        return <Warning color="warning" {...iconProps} />
    }
  }

  const getConfirmButtonColor = () => {
    switch (type) {
      case 'error':
        return 'error'
      case 'success':
        return 'success'
      case 'info':
        return 'info'
      default:
        return 'warning'
    }
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          p: 1
        }
      }}
    >
      <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
        {title}
      </DialogTitle>
      
      <DialogContent>
        <Box
          display="flex"
          flexDirection="column"
          alignItems="center"
          textAlign="center"
        >
          {getIcon()}
          <Typography variant="body1" color="text.secondary">
            {message}
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions sx={{ px: 3, pb: 3, gap: 1 }}>
        <Button
          onClick={onClose}
          variant="outlined"
          disabled={loading}
          fullWidth
        >
          {cancelText}
        </Button>
        <Button
          onClick={onConfirm}
          variant="contained"
          color={getConfirmButtonColor()}
          disabled={loading}
          fullWidth
        >
          {loading ? 'Processing...' : confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default ConfirmDialog
