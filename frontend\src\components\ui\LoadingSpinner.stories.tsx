import type { Meta, StoryObj } from '@storybook/react'
import LoadingSpinner from './LoadingSpinner'

const meta: Meta<typeof LoadingSpinner> = {
  title: 'UI/LoadingSpinner',
  component: LoadingSpinner,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A customizable loading spinner component with optional message and full-screen mode.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    size: {
      control: { type: 'number', min: 20, max: 100, step: 10 },
      description: 'Size of the spinner in pixels',
    },
    message: {
      control: 'text',
      description: 'Optional message to display below the spinner',
    },
    fullScreen: {
      control: 'boolean',
      description: 'Whether to display the spinner in full-screen mode',
    },
    color: {
      control: 'select',
      options: ['primary', 'secondary', 'inherit'],
      description: 'Color of the spinner',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    size: 40,
    message: 'Loading...',
    fullScreen: false,
    color: 'primary',
  },
}

export const Small: Story = {
  args: {
    size: 24,
    message: 'Loading...',
    color: 'primary',
  },
}

export const Large: Story = {
  args: {
    size: 60,
    message: 'Loading content...',
    color: 'primary',
  },
}

export const WithoutMessage: Story = {
  args: {
    size: 40,
    color: 'primary',
  },
}

export const Secondary: Story = {
  args: {
    size: 40,
    message: 'Processing...',
    color: 'secondary',
  },
}

export const FullScreen: Story = {
  args: {
    size: 60,
    message: 'Loading application...',
    fullScreen: true,
    color: 'primary',
  },
  parameters: {
    layout: 'fullscreen',
  },
}

export const CustomMessage: Story = {
  args: {
    size: 50,
    message: 'Uploading your files...',
    color: 'primary',
  },
}
