import {
  Drawer,
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Button,
  Divider,
  Card,
  CardContent,
  CardMedia,
  Grid
} from '@mui/material'
import {
  TrendingUp,
  People,
  Store
} from '@mui/icons-material'

const RightSidebar = ({ open, onClose, variant = 'permanent', width = 320 }) => {
  // Mock data
  const trendingTopics = [
    { tag: '#ReactJS', posts: '12.5K posts' },
    { tag: '#WebDev', posts: '8.2K posts' },
    { tag: '#JavaScript', posts: '15.1K posts' },
    { tag: '#TypeScript', posts: '6.8K posts' },
  ]

  const suggestedUsers = [
    { id: 1, name: '<PERSON>', username: 'johndo<PERSON>', avatar: null },
    { id: 2, name: '<PERSON>', username: 'jane<PERSON>', avatar: null },
    { id: 3, name: '<PERSON>', username: 'mike<PERSON>', avatar: null },
  ]

  const featuredProducts = [
    { id: 1, name: 'Cool T-Shirt', price: '$25', image: null },
    { id: 2, name: 'Awesome Mug', price: '$15', image: null },
    { id: 3, name: '<PERSON>adget', price: '$99', image: null },
  ]

  const sidebarContent = (
    <Box sx={{ width: width, p: 2, height: '100%', overflow: 'auto' }}>
      {/* Trending Topics */}
      <Paper sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <TrendingUp color="primary" />
          Trending
        </Typography>
        <List dense>
          {trendingTopics.map((topic, index) => (
            <ListItem key={index} sx={{ px: 0 }}>
              <ListItemText
                primary={topic.tag}
                secondary={topic.posts}
                primaryTypographyProps={{ fontWeight: 600, color: 'primary.main' }}
              />
            </ListItem>
          ))}
        </List>
      </Paper>

      {/* Suggested Users */}
      <Paper sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <People color="primary" />
          Who to follow
        </Typography>
        <List>
          {suggestedUsers.map((user) => (
            <ListItem key={user.id} sx={{ px: 0 }}>
              <ListItemAvatar>
                <Avatar src={user.avatar}>
                  {user.name.charAt(0)}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={user.name}
                secondary={`@${user.username}`}
              />
              <Button size="small" variant="outlined">
                Follow
              </Button>
            </ListItem>
          ))}
        </List>
        <Divider sx={{ my: 1 }} />
        <Button fullWidth variant="text" color="primary">
          Show more
        </Button>
      </Paper>

      {/* Featured Products */}
      <Paper sx={{ p: 2, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Store color="primary" />
          Featured Products
        </Typography>
        <Grid container spacing={1}>
          {featuredProducts.map((product) => (
            <Grid item xs={12} key={product.id}>
              <Card sx={{ display: 'flex', mb: 1 }}>
                <CardMedia
                  component="div"
                  sx={{
                    width: 60,
                    height: 60,
                    bgcolor: 'grey.200',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Store color="disabled" />
                </CardMedia>
                <CardContent sx={{ flex: 1, p: 1, '&:last-child': { pb: 1 } }}>
                  <Typography variant="body2" fontWeight={600} noWrap>
                    {product.name}
                  </Typography>
                  <Typography variant="body2" color="primary">
                    {product.price}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
        <Button fullWidth variant="outlined" sx={{ mt: 1 }}>
          View All Products
        </Button>
      </Paper>
    </Box>
  )

  return (
    <Drawer
      variant={variant}
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        width: width,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: width,
          boxSizing: 'border-box',
          borderLeft: 1,
          borderColor: 'divider',
        },
      }}
    >
      {sidebarContent}
    </Drawer>
  )
}

export default RightSidebar
