/**
 * Validate email address format
 * @param {string} email - Email address to validate
 * @returns {boolean} True if email is valid, false otherwise
 */
const validateEmail = (email) => {
  if (!email || typeof email !== 'string') {
    return false
  }

  // Basic email regex pattern
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  
  // Additional checks
  const isValidFormat = emailRegex.test(email)
  const isValidLength = email.length <= 254 // RFC 5321 limit
  const hasValidLocalPart = email.split('@')[0]?.length <= 64 // RFC 5321 limit
  
  return isValidFormat && isValidLength && hasValidLocalPart
}

/**
 * Get email validation error message
 * @param {string} email - Email address to validate
 * @returns {string|null} Error message or null if valid
 */
export const getEmailValidationError = (email) => {
  if (!email) {
    return 'Email is required'
  }
  
  if (typeof email !== 'string') {
    return 'Email must be a string'
  }
  
  if (email.length > 254) {
    return 'Email is too long (max 254 characters)'
  }
  
  if (email.split('@')[0]?.length > 64) {
    return 'Email local part is too long (max 64 characters)'
  }
  
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    return 'Please enter a valid email address'
  }
  
  return null
}

export default validateEmail
