// Application Constants

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    ME: '/api/auth/me',
    REFRESH: '/api/auth/refresh-token',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
    CHANGE_PASSWORD: '/api/auth/change-password',
  },
  POSTS: {
    BASE: '/api/posts',
    USER: (userId) => `/api/posts/user/${userId}`,
    SINGLE: (postId) => `/api/posts/${postId}`,
    SEARCH: '/api/posts/search',
    SAVED: '/api/posts/saved',
    SAVE: '/api/posts/save',
  },
  USERS: {
    BASE: '/api/users',
    ME: '/api/users/me',
    PROFILE: (userId) => `/api/users/${userId}`,
    SEARCH: '/api/users/search',
    SUGGESTED: '/api/users/suggested',
  },
  FEED: {
    FOLLOWING: '/api/feed/following',
    TRENDING: '/api/feed/trending',
    EXPLORE: '/api/feed/explore',
  },
  MESSAGES: {
    BASE: '/api/messages',
    CONVERSATIONS: '/api/conversations',
    CONVERSATION: (id) => `/api/conversations/${id}`,
  },
  NOTIFICATIONS: {
    BASE: '/api/notifications',
    MARK_READ: '/api/notifications/mark-read',
    PREFERENCES: '/api/users/me/notification-preferences',
  },
  SHOP: {
    PRODUCTS: '/api/shop/products',
    FEATURED: '/api/shop/products/featured',
    CATEGORIES: '/api/shop/categories',
    CART: '/api/shop/cart',
    ORDERS: '/api/shop/orders',
  },
  LIVE: {
    STREAMS: '/api/live-streams',
    STREAM: (id) => `/api/live-streams/${id}`,
    RECORDINGS: '/api/live-streams/recordings',
  }
}

// File Upload Limits
export const FILE_LIMITS = {
  IMAGE: {
    MAX_SIZE: 5 * 1024 * 1024, // 5MB
    ACCEPTED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  },
  VIDEO: {
    MAX_SIZE: 100 * 1024 * 1024, // 100MB
    ACCEPTED_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
  },
  AUDIO: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ACCEPTED_TYPES: ['audio/mp3', 'audio/wav', 'audio/ogg'],
  },
  DOCUMENT: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ACCEPTED_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  }
}

// UI Constants
export const UI_CONSTANTS = {
  SIDEBAR_WIDTH: 280,
  HEADER_HEIGHT: 64,
  MOBILE_BREAKPOINT: 768,
  TABLET_BREAKPOINT: 1024,
  DESKTOP_BREAKPOINT: 1200,
  
  // Animation durations
  ANIMATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  
  // Z-index layers
  Z_INDEX: {
    DRAWER: 1200,
    MODAL: 1300,
    SNACKBAR: 1400,
    TOOLTIP: 1500,
  }
}

// Post Types
export const POST_TYPES = {
  TEXT: 'text',
  IMAGE: 'image',
  VIDEO: 'video',
  AUDIO: 'audio',
  POLL: 'poll',
  STORY: 'story',
  REEL: 'reel',
}

// Privacy Settings
export const PRIVACY_LEVELS = {
  PUBLIC: 'public',
  FRIENDS: 'friends',
  PRIVATE: 'private',
  CUSTOM: 'custom',
}

// Notification Types
export const NOTIFICATION_TYPES = {
  LIKE: 'like',
  COMMENT: 'comment',
  FOLLOW: 'follow',
  MESSAGE: 'message',
  MENTION: 'mention',
  POST: 'post',
  STORY: 'story',
  LIVE: 'live',
}

// Socket Events
export const SOCKET_EVENTS = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  MESSAGE: 'message',
  NOTIFICATION: 'notification',
  TYPING: 'typing',
  ONLINE_STATUS: 'online_status',
  LIVE_STREAM: 'live_stream',
  POST_UPDATE: 'post_update',
}

// Theme Settings
export const THEME_MODES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
}

// Local Storage Keys
export const STORAGE_KEYS = {
  TOKEN: 'token',
  USER: 'user',
  THEME: 'theme',
  LANGUAGE: 'language',
  NOTIFICATIONS: 'notifications',
  DRAFT_POST: 'draft_post',
}

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION: 'Please check your input and try again.',
  FILE_TOO_LARGE: 'File size is too large.',
  INVALID_FILE_TYPE: 'Invalid file type.',
}

// Success Messages
export const SUCCESS_MESSAGES = {
  POST_CREATED: 'Post created successfully!',
  POST_UPDATED: 'Post updated successfully!',
  POST_DELETED: 'Post deleted successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  PASSWORD_CHANGED: 'Password changed successfully!',
  EMAIL_VERIFIED: 'Email verified successfully!',
  SETTINGS_SAVED: 'Settings saved successfully!',
}

// Regex Patterns
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
  PHONE: /^\+?[\d\s-()]+$/,
  URL: /^https?:\/\/.+/,
  HASHTAG: /#[\w]+/g,
  MENTION: /@[\w]+/g,
}

// Mood Options
export const MOOD_OPTIONS = [
  { value: 'happy', label: 'Happy', emoji: '😊', color: '#4caf50' },
  { value: 'sad', label: 'Sad', emoji: '😢', color: '#2196f3' },
  { value: 'excited', label: 'Excited', emoji: '🤩', color: '#ff9800' },
  { value: 'angry', label: 'Angry', emoji: '😠', color: '#f44336' },
  { value: 'love', label: 'Love', emoji: '😍', color: '#e91e63' },
  { value: 'surprised', label: 'Surprised', emoji: '😲', color: '#9c27b0' },
  { value: 'neutral', label: 'Neutral', emoji: '😐', color: '#607d8b' },
  { value: 'grateful', label: 'Grateful', emoji: '🙏', color: '#795548' },
  { value: 'motivated', label: 'Motivated', emoji: '💪', color: '#ff5722' },
  { value: 'relaxed', label: 'Relaxed', emoji: '😌', color: '#009688' },
]

export default {
  API_ENDPOINTS,
  FILE_LIMITS,
  UI_CONSTANTS,
  POST_TYPES,
  PRIVACY_LEVELS,
  NOTIFICATION_TYPES,
  SOCKET_EVENTS,
  THEME_MODES,
  STORAGE_KEYS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  REGEX_PATTERNS,
  MOOD_OPTIONS,
}
