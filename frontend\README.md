# Let's Talk - Modern Frontend

A cutting-edge social media platform frontend built with React, TypeScript, and modern web technologies.

## 🚀 Features

### Core Technologies
- **React 19** with modern hooks and Suspense
- **TypeScript** for type safety and better DX
- **Vite** for lightning-fast development
- **Material-UI v7** with custom theming
- **React Query** for advanced caching and state management
- **React Router v7** for navigation

### Modern Architecture
- **Component-driven development** with Storybook
- **Comprehensive testing** with Jest and React Testing Library
- **PWA capabilities** for mobile app experience
- **Code splitting** for optimal performance
- **Service Worker** for offline functionality

### UI/UX Features
- **Responsive design** for all devices
- **Dark/Light theme** support
- **Real-time updates** via Socket.IO
- **Infinite scrolling** for feeds
- **Image/video upload** with preview
- **Emoji picker** and mood selection
- **Push notifications** support

## 🛠️ Development

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation
```bash
cd frontend
npm install
```

### Development Scripts
```bash
# Start development server
npm run dev

# Type checking
npm run type-check

# Run tests
npm run test
npm run test:watch
npm run test:coverage

# Storybook
npm run storybook

# Build for production
npm run build

# Preview production build
npm run preview
```

## 🧪 Testing

### Unit Tests
- **Jest** for test runner
- **React Testing Library** for component testing
- **MSW** for API mocking
- **Coverage reports** with detailed metrics

```bash
# Run all tests
npm run test

# Watch mode for development
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## 📱 PWA Features

### Installation
- **Add to Home Screen** prompt
- **Offline functionality** with service worker
- **Background sync** for pending actions
- **Push notifications** support

### Caching Strategy
- **Static assets**: Cache first
- **API responses**: Network first with cache fallback
- **Images**: Cache first with network fallback

## 🎨 Theming

### Custom Theme System
- **Light/Dark modes** with system preference detection
- **Material-UI customization** with brand colors
- **Responsive breakpoints** for all devices
- **Typography scale** optimized for readability

## 🔄 State Management

### React Query
- **Server state management** with caching
- **Optimistic updates** for better UX
- **Background refetching** and synchronization
- **Error handling** with retry logic

## 🚀 Performance

### Optimization Techniques
- **Code splitting** with React.lazy()
- **Bundle analysis** with webpack-bundle-analyzer
- **Image optimization** with lazy loading
- **Memoization** for expensive computations

## 🚀 Deployment

### Build Process
```bash
# Type check and build
npm run build

# Analyze bundle size
npm run analyze
```

### Environment Variables
```env
VITE_API_URL=http://localhost:10002
VITE_SOCKET_URL=http://localhost:10002
VITE_VAPID_PUBLIC_KEY=your-vapid-key
```

---

Built with ❤️ by the Let's Talk team
