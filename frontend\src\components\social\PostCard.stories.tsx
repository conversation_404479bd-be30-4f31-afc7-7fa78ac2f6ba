import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import PostCard from './PostCard'
import type { Post } from '@/types'

// Mock data
const mockUser = {
  id: '1',
  username: 'joh<PERSON><PERSON>',
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  verified: true,
  followersCount: 1250,
  followingCount: 180,
  postsCount: 45,
  createdAt: '2023-01-15T10:00:00Z',
  updatedAt: '2024-01-15T10:00:00Z',
}

const mockPost: Post = {
  id: '1',
  content: 'Just had an amazing day at the beach! The sunset was absolutely breathtaking. Sometimes you need to take a step back and appreciate the simple things in life. 🌅 #sunset #beach #grateful',
  media: [
    {
      id: '1',
      url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop',
      type: 'image',
      size: 1024000,
      filename: 'sunset.jpg',
    }
  ],
  author: mockUser,
  likesCount: 42,
  commentsCount: 8,
  sharesCount: 3,
  isLiked: false,
  isSaved: false,
  mood: {
    value: 'grateful',
    label: 'Grateful',
    emoji: '🙏',
    color: '#4caf50',
  },
  tags: ['sunset', 'beach', 'grateful'],
  privacy: 'public',
  createdAt: '2024-01-15T18:30:00Z',
  updatedAt: '2024-01-15T18:30:00Z',
}

const meta: Meta<typeof PostCard> = {
  title: 'Social/PostCard',
  component: PostCard,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A social media post card component with like, comment, share, and save functionality.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    post: {
      description: 'Post data object',
    },
    onLike: {
      action: 'liked',
      description: 'Callback when post is liked',
    },
    onComment: {
      action: 'commented',
      description: 'Callback when comment button is clicked',
    },
    onShare: {
      action: 'shared',
      description: 'Callback when post is shared',
    },
    onSave: {
      action: 'saved',
      description: 'Callback when post is saved',
    },
    onEdit: {
      action: 'edited',
      description: 'Callback when post is edited',
    },
    onDelete: {
      action: 'deleted',
      description: 'Callback when post is deleted',
    },
    onReport: {
      action: 'reported',
      description: 'Callback when post is reported',
    },
    showComments: {
      control: 'boolean',
      description: 'Whether to show comments section',
    },
  },
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    post: mockPost,
    showComments: false,
  },
}

export const Liked: Story = {
  args: {
    post: {
      ...mockPost,
      isLiked: true,
      likesCount: 43,
    },
    showComments: false,
  },
}

export const Saved: Story = {
  args: {
    post: {
      ...mockPost,
      isSaved: true,
    },
    showComments: false,
  },
}

export const WithoutMedia: Story = {
  args: {
    post: {
      ...mockPost,
      media: [],
      content: 'Just sharing some thoughts about life and happiness. Sometimes the best moments are the quiet ones where you can just reflect and be grateful for what you have. 💭 #thoughts #gratitude #mindfulness',
    },
    showComments: false,
  },
}

export const WithoutMood: Story = {
  args: {
    post: {
      ...mockPost,
      mood: undefined,
    },
    showComments: false,
  },
}

export const LongContent: Story = {
  args: {
    post: {
      ...mockPost,
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium.',
    },
    showComments: false,
  },
}

export const HighEngagement: Story = {
  args: {
    post: {
      ...mockPost,
      likesCount: 1250,
      commentsCount: 89,
      sharesCount: 34,
      isLiked: true,
    },
    showComments: false,
  },
}

export const WithComments: Story = {
  args: {
    post: mockPost,
    showComments: true,
  },
}
