import React, { useState } from 'react'
import { Box, useMediaQuery, useTheme } from '@mui/material'
import { ErrorBoundary } from '../ui'
import Sidebar from './Sidebar'
import Header from './Header'
import MobileNavigation from './MobileNavigation'
import RightSidebar from './RightSidebar'

const AppLayout = ({ children, showRightSidebar = true }) => {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const isTablet = useMediaQuery(theme.breakpoints.down('lg'))
  
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile)
  const [rightSidebarOpen, setRightSidebarOpen] = useState(!isTablet && showRightSidebar)

  const sidebarWidth = 280
  const rightSidebarWidth = 320

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const handleRightSidebarToggle = () => {
    setRightSidebarOpen(!rightSidebarOpen)
  }

  return (
    <ErrorBoundary>
      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        {/* Left Sidebar */}
        <Sidebar
          open={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          variant={isMobile ? 'temporary' : 'permanent'}
          width={sidebarWidth}
        />

        {/* Main Content Area */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100vh',
            marginLeft: isMobile ? 0 : sidebarOpen ? `${sidebarWidth}px` : 0,
            marginRight: !isMobile && rightSidebarOpen && showRightSidebar ? `${rightSidebarWidth}px` : 0,
            transition: theme.transitions.create(['margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
          }}
        >
          {/* Header */}
          <Header
            onSidebarToggle={handleSidebarToggle}
            onRightSidebarToggle={handleRightSidebarToggle}
            sidebarOpen={sidebarOpen}
            rightSidebarOpen={rightSidebarOpen}
            showRightSidebarToggle={showRightSidebar}
          />

          {/* Page Content */}
          <Box
            sx={{
              flexGrow: 1,
              p: { xs: 1, sm: 2, md: 3 },
              bgcolor: 'background.default',
              minHeight: 'calc(100vh - 64px)', // Subtract header height
            }}
          >
            <ErrorBoundary fallbackMessage="Something went wrong with this page content.">
              {children}
            </ErrorBoundary>
          </Box>
        </Box>

        {/* Right Sidebar */}
        {showRightSidebar && (
          <RightSidebar
            open={rightSidebarOpen}
            onClose={() => setRightSidebarOpen(false)}
            variant={isMobile ? 'temporary' : 'permanent'}
            width={rightSidebarWidth}
          />
        )}

        {/* Mobile Bottom Navigation */}
        {isMobile && (
          <MobileNavigation />
        )}
      </Box>
    </ErrorBoundary>
  )
}

export default AppLayout
