const cloudinary = require('cloudinary').v2;

// Configure Cloudinary with environment variables
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME || 'dqhawepog',
  api_key: process.env.CLOUDINARY_API_KEY || '634117326758731',
  api_secret: process.env.CLOUDINARY_API_SECRET || 'oo7aOjnDjJvafwDxck3Dga0XkJi8',
  secure: true
});

// Helper function to upload image to Cloudinary
const uploadImage = async (filePath, options = {}) => {
  try {
    const defaultOptions = {
      folder: 'letstalk',
      resource_type: 'auto',
      quality: 'auto',
      fetch_format: 'auto'
    };

    const uploadOptions = { ...defaultOptions, ...options };
    const result = await cloudinary.uploader.upload(filePath, uploadOptions);
    
    return {
      success: true,
      url: result.secure_url,
      public_id: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes
    };
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Helper function to upload video to Cloudinary
const uploadVideo = async (filePath, options = {}) => {
  try {
    const defaultOptions = {
      folder: 'letstalk/videos',
      resource_type: 'video',
      quality: 'auto',
      format: 'mp4'
    };

    const uploadOptions = { ...defaultOptions, ...options };
    const result = await cloudinary.uploader.upload(filePath, uploadOptions);
    
    return {
      success: true,
      url: result.secure_url,
      public_id: result.public_id,
      width: result.width,
      height: result.height,
      format: result.format,
      duration: result.duration,
      bytes: result.bytes
    };
  } catch (error) {
    console.error('Cloudinary video upload error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Helper function to delete file from Cloudinary
const deleteFile = async (publicId, resourceType = 'image') => {
  try {
    const result = await cloudinary.uploader.destroy(publicId, {
      resource_type: resourceType
    });
    
    return {
      success: result.result === 'ok',
      result: result.result
    };
  } catch (error) {
    console.error('Cloudinary delete error:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Helper function to generate optimized image URL
const getOptimizedImageUrl = (publicId, options = {}) => {
  try {
    const defaultOptions = {
      quality: 'auto',
      fetch_format: 'auto',
      crop: 'fill',
      gravity: 'auto'
    };

    const transformOptions = { ...defaultOptions, ...options };
    return cloudinary.url(publicId, transformOptions);
  } catch (error) {
    console.error('Cloudinary URL generation error:', error);
    return null;
  }
};

// Helper function to generate thumbnail
const generateThumbnail = (publicId, width = 300, height = 300) => {
  try {
    return cloudinary.url(publicId, {
      width,
      height,
      crop: 'fill',
      gravity: 'auto',
      quality: 'auto',
      fetch_format: 'auto'
    });
  } catch (error) {
    console.error('Cloudinary thumbnail generation error:', error);
    return null;
  }
};

// Helper function to upload multiple files
const uploadMultipleFiles = async (files, options = {}) => {
  try {
    const uploadPromises = files.map(file => {
      const fileOptions = {
        ...options,
        folder: options.folder || 'letstalk'
      };
      return uploadImage(file.path || file, fileOptions);
    });

    const results = await Promise.all(uploadPromises);
    return results;
  } catch (error) {
    console.error('Cloudinary multiple upload error:', error);
    return [];
  }
};

// Helper function to create image transformations
const createTransformation = (publicId, transformations) => {
  try {
    return cloudinary.url(publicId, transformations);
  } catch (error) {
    console.error('Cloudinary transformation error:', error);
    return null;
  }
};

module.exports = {
  cloudinary,
  uploadImage,
  uploadVideo,
  deleteFile,
  getOptimizedImageUrl,
  generateThumbnail,
  uploadMultipleFiles,
  createTransformation,
  uploader: cloudinary.uploader,
  url: cloudinary.url
};
