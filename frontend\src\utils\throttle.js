/**
 * Throttle function - limits execution to at most once per specified time period
 * @param {Function} func - Function to throttle
 * @param {number} limit - Time limit in milliseconds
 * @param {Object} options - Options object
 * @param {boolean} options.leading - Execute on leading edge (default: true)
 * @param {boolean} options.trailing - Execute on trailing edge (default: true)
 * @returns {Function} Throttled function
 */
const throttle = (func, limit, options = {}) => {
  const { leading = true, trailing = true } = options
  let inThrottle
  let lastFunc
  let lastRan

  return function executedFunction(...args) {
    if (!inThrottle) {
      if (leading) {
        func.apply(this, args)
        lastRan = Date.now()
      }
      inThrottle = true
    } else {
      if (trailing) {
        clearTimeout(lastFunc)
        lastFunc = setTimeout(() => {
          if (Date.now() - lastRan >= limit) {
            func.apply(this, args)
            lastRan = Date.now()
          }
        }, limit - (Date.now() - lastRan))
      }
    }

    setTimeout(() => {
      inThrottle = false
    }, limit)
  }
}

/**
 * Simple throttle implementation
 * @param {Function} func - Function to throttle
 * @param {number} delay - Delay in milliseconds
 * @returns {Function} Throttled function
 */
export const simpleThrottle = (func, delay) => {
  let timeoutId
  let lastExecTime = 0

  return function executedFunction(...args) {
    const currentTime = Date.now()

    if (currentTime - lastExecTime > delay) {
      func.apply(this, args)
      lastExecTime = currentTime
    } else {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        func.apply(this, args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

/**
 * Throttle for scroll events
 * @param {Function} func - Function to throttle
 * @param {number} delay - Delay in milliseconds (default: 100)
 * @returns {Function} Throttled function optimized for scroll
 */
export const throttleScroll = (func, delay = 100) => {
  let ticking = false

  return function executedFunction(...args) {
    if (!ticking) {
      requestAnimationFrame(() => {
        func.apply(this, args)
        ticking = false
      })
      ticking = true
    }
  }
}

/**
 * Throttle for resize events
 * @param {Function} func - Function to throttle
 * @param {number} delay - Delay in milliseconds (default: 250)
 * @returns {Function} Throttled function optimized for resize
 */
export const throttleResize = (func, delay = 250) => {
  let timeoutId

  return function executedFunction(...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      func.apply(this, args)
    }, delay)
  }
}

export default throttle
