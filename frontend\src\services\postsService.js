import axios from '../utils/fixedAxios'

class PostsService {
  // Get all posts
  async getPosts(params = {}) {
    const response = await axios.get('/api/posts', { params })
    return response.data.data || response.data.posts || []
  }

  // Get posts by user
  async getUserPosts(userId, params = {}) {
    const response = await axios.get(`/api/posts/user/${userId}`, { params })
    return response.data.data || response.data.posts || []
  }

  // Get single post
  async getPost(postId) {
    const response = await axios.get(`/api/posts/${postId}`)
    return response.data.data || response.data.post
  }

  // Create post
  async createPost(postData) {
    const formData = new FormData()
    
    // Handle text content
    if (postData.content) {
      formData.append('content', postData.content)
    }
    
    // Handle mood
    if (postData.mood) {
      formData.append('mood', postData.mood)
    }
    
    // Handle tags
    if (postData.tags && postData.tags.length > 0) {
      postData.tags.forEach(tag => {
        formData.append('tags[]', tag)
      })
    }
    
    // Handle media files
    if (postData.media && postData.media.length > 0) {
      postData.media.forEach((file, index) => {
        formData.append('media', file)
      })
    }
    
    // Handle privacy settings
    if (postData.privacy) {
      formData.append('privacy', postData.privacy)
    }

    const response = await axios.post('/api/posts', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    
    return response.data.data || response.data.post
  }

  // Update post
  async updatePost(postId, updateData) {
    const response = await axios.put(`/api/posts/${postId}`, updateData)
    return response.data.data || response.data.post
  }

  // Delete post
  async deletePost(postId) {
    const response = await axios.delete(`/api/posts/${postId}`)
    return response.data
  }

  // Like post
  async likePost(postId) {
    const response = await axios.post(`/api/likes`, { 
      targetType: 'post', 
      targetId: postId 
    })
    return response.data
  }

  // Unlike post
  async unlikePost(postId) {
    const response = await axios.delete(`/api/likes`, {
      data: { targetType: 'post', targetId: postId }
    })
    return response.data
  }

  // Get post comments
  async getComments(postId, params = {}) {
    const response = await axios.get(`/api/comments/post/${postId}`, { params })
    return response.data.data || response.data.comments || []
  }

  // Add comment
  async addComment(postId, content) {
    const response = await axios.post('/api/comments', {
      postId,
      content
    })
    return response.data.data || response.data.comment
  }

  // Get trending posts
  async getTrendingPosts(params = {}) {
    const response = await axios.get('/api/feed/trending', { params })
    return response.data.data || response.data.posts || []
  }

  // Get following posts
  async getFollowingPosts(params = {}) {
    const response = await axios.get('/api/feed/following', { params })
    return response.data.data || response.data.posts || []
  }

  // Search posts
  async searchPosts(query, params = {}) {
    const response = await axios.get('/api/posts/search', {
      params: { q: query, ...params }
    })
    return response.data.data || response.data.posts || []
  }

  // Report post
  async reportPost(postId, reason) {
    const response = await axios.post('/api/moderation/report', {
      targetType: 'post',
      targetId: postId,
      reason
    })
    return response.data
  }

  // Save/Unsave post
  async toggleSavePost(postId) {
    const response = await axios.post('/api/posts/save', { postId })
    return response.data
  }

  // Get saved posts
  async getSavedPosts(params = {}) {
    const response = await axios.get('/api/posts/saved', { params })
    return response.data.data || response.data.posts || []
  }
}

export default new PostsService()
