import React from 'react'
import {
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Typography,
  Box,
  IconButton,
  Chip,
  useTheme
} from '@mui/material'
import {
  Favorite,
  Comment,
  PersonAdd,
  Share,
  Visibility,
  ShoppingCart,
  Notifications,
  Close,
  Circle
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { formatDistanceToNow } from 'date-fns'
import { useNotifications } from '../../hooks'

const NotificationItem = ({ notification, onClose, compact = false }) => {
  const theme = useTheme()
  const navigate = useNavigate()
  const { markAsRead, deleteNotification } = useNotifications()

  const getNotificationIcon = (type) => {
    const iconProps = { fontSize: 'small', color: 'primary' }
    
    switch (type) {
      case 'like_post':
      case 'like_reel':
      case 'like_comment':
      case 'like_mood':
        return <Favorite {...iconProps} sx={{ color: '#e91e63' }} />
      case 'comment_post':
      case 'comment_reel':
      case 'comment_mood':
      case 'reply_comment':
        return <Comment {...iconProps} sx={{ color: '#2196f3' }} />
      case 'follow':
      case 'follow_request':
      case 'follow_accept':
        return <PersonAdd {...iconProps} sx={{ color: '#4caf50' }} />
      case 'story_view':
        return <Visibility {...iconProps} sx={{ color: '#ff9800' }} />
      case 'order_placed':
      case 'order_confirmed':
      case 'payment_received':
        return <ShoppingCart {...iconProps} sx={{ color: '#9c27b0' }} />
      default:
        return <Notifications {...iconProps} />
    }
  }

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent':
        return '#f44336'
      case 'high':
        return '#ff9800'
      case 'normal':
        return '#2196f3'
      case 'low':
        return '#4caf50'
      default:
        return '#2196f3'
    }
  }

  const handleClick = async () => {
    // Mark as read if unread
    if (!notification.read) {
      await markAsRead(notification.id)
    }

    // Navigate to relevant page
    if (notification.actionUrl) {
      navigate(notification.actionUrl)
    } else {
      // Default navigation based on type
      const { type, data } = notification
      
      if (type.includes('post') && data?.postId) {
        navigate(`/posts/${data.postId}`)
      } else if (type.includes('reel') && data?.reelId) {
        navigate(`/reels/${data.reelId}`)
      } else if (type.includes('follow') && data?.senderId) {
        navigate(`/profile/${data.senderId}`)
      } else if (type.includes('message')) {
        navigate('/messages')
      } else if (type.includes('order')) {
        navigate('/orders')
      }
    }

    if (onClose) onClose()
  }

  const handleDelete = async (e) => {
    e.stopPropagation()
    await deleteNotification(notification.id)
  }

  const formatTimeAgo = (date) => {
    try {
      return formatDistanceToNow(new Date(date), { addSuffix: true })
    } catch {
      return notification.timeAgo || 'Recently'
    }
  }

  return (
    <ListItem
      sx={{
        cursor: 'pointer',
        backgroundColor: notification.read 
          ? 'transparent' 
          : theme.palette.mode === 'dark' 
            ? 'rgba(144, 202, 249, 0.08)' 
            : 'rgba(25, 118, 210, 0.08)',
        '&:hover': {
          backgroundColor: theme.palette.mode === 'dark'
            ? 'rgba(255, 255, 255, 0.05)'
            : 'rgba(0, 0, 0, 0.04)'
        },
        position: 'relative',
        py: compact ? 1 : 1.5,
        px: 2
      }}
      onClick={handleClick}
    >
      {/* Unread indicator */}
      {!notification.read && (
        <Circle
          sx={{
            position: 'absolute',
            left: 8,
            top: '50%',
            transform: 'translateY(-50%)',
            fontSize: 8,
            color: getPriorityColor(notification.priority)
          }}
        />
      )}

      <ListItemAvatar sx={{ ml: !notification.read ? 1 : 0 }}>
        <Box position="relative">
          <Avatar
            src={notification.sender?.profilePicture}
            sx={{ 
              width: compact ? 36 : 44, 
              height: compact ? 36 : 44,
              border: `2px solid ${getPriorityColor(notification.priority)}20`
            }}
          >
            {notification.sender?.name?.charAt(0)?.toUpperCase() || 'S'}
          </Avatar>
          <Box
            sx={{
              position: 'absolute',
              bottom: -2,
              right: -2,
              backgroundColor: 'background.paper',
              borderRadius: '50%',
              p: 0.25,
              border: `1px solid ${theme.palette.divider}`
            }}
          >
            {getNotificationIcon(notification.type)}
          </Box>
        </Box>
      </ListItemAvatar>

      <ListItemText
        primary={
          <Box display="flex" alignItems="center" gap={1} mb={0.5}>
            <Typography
              variant={compact ? 'body2' : 'subtitle2'}
              fontWeight={notification.read ? 400 : 600}
              sx={{ flex: 1 }}
            >
              {notification.title || notification.message}
            </Typography>
            {notification.priority !== 'normal' && (
              <Chip
                label={notification.priority}
                size="small"
                sx={{
                  height: 18,
                  fontSize: '0.7rem',
                  backgroundColor: getPriorityColor(notification.priority),
                  color: 'white'
                }}
              />
            )}
          </Box>
        }
        secondary={
          <Box>
            {notification.title && notification.message && (
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mb: 0.5 }}
              >
                {notification.message}
              </Typography>
            )}
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
            >
              {formatTimeAgo(notification.createdAt)}
              {notification.actionText && (
                <Chip
                  label={notification.actionText}
                  size="small"
                  variant="outlined"
                  sx={{ height: 16, fontSize: '0.65rem' }}
                />
              )}
            </Typography>
          </Box>
        }
      />

      <IconButton
        size="small"
        onClick={handleDelete}
        sx={{
          opacity: 0.6,
          '&:hover': { opacity: 1 }
        }}
      >
        <Close fontSize="small" />
      </IconButton>
    </ListItem>
  )
}

export default NotificationItem
