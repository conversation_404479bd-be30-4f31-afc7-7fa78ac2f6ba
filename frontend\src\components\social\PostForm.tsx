import { useState } from 'react'
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Box,
  Avatar,
  Typography,
  IconButton,
  Chip,
  Paper
} from '@mui/material'
import {
  Close,
  Image,
  Mood,
  LocationOn,
  Poll
} from '@mui/icons-material'
import { useAuth } from '@/context/AuthContext'
import type { PostForm as PostFormData } from '@/types'

interface PostFormProps {
  open?: boolean
  onClose?: () => void
  onSubmit: (data: PostFormData) => void
  compact?: boolean
  placeholder?: string
  fullScreen?: boolean
}

const PostForm = ({
  open = false,
  onClose,
  onSubmit,
  compact = false,
  placeholder = "What's happening?",
  fullScreen = false
}: PostFormProps) => {
  const { user } = useAuth()
  const [content, setContent] = useState('')
  const [selectedMood, setSelectedMood] = useState<string>('')
  const [privacy, setPrivacy] = useState<'public' | 'friends' | 'private'>('public')

  const handleSubmit = () => {
    if (!content.trim()) return

    const postData: PostFormData = {
      content: content.trim(),
      privacy,
      mood: selectedMood || undefined
    }

    onSubmit(postData)
    setContent('')
    setSelectedMood('')
    setPrivacy('public')
  }

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      handleSubmit()
    }
  }

  // Compact version for sidebar
  if (compact) {
    return (
      <Paper sx={{ p: 2, borderRadius: 2 }}>
        <Box display="flex" gap={2}>
          <Avatar src={user?.avatar} sx={{ width: 40, height: 40 }}>
            {user?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <Box flex={1}>
            <TextField
              fullWidth
              multiline
              rows={2}
              placeholder={placeholder}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              onKeyPress={handleKeyPress}
              variant="outlined"
              size="small"
            />
            <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
              <Box display="flex" gap={1}>
                <IconButton size="small" color="primary">
                  <Image />
                </IconButton>
                <IconButton size="small" color="primary">
                  <Mood />
                </IconButton>
              </Box>
              <Button
                variant="contained"
                size="small"
                onClick={handleSubmit}
                disabled={!content.trim()}
              >
                Post
              </Button>
            </Box>
          </Box>
        </Box>
      </Paper>
    )
  }

  // Full dialog version
  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">Create Post</Typography>
          <IconButton onClick={onClose} size="small">
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box display="flex" gap={2} mb={2}>
          <Avatar src={user?.avatar} sx={{ width: 48, height: 48 }}>
            {user?.name?.charAt(0)?.toUpperCase()}
          </Avatar>
          <Box>
            <Typography variant="subtitle1" fontWeight={600}>
              {user?.name}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              @{user?.username}
            </Typography>
          </Box>
        </Box>

        <TextField
          fullWidth
          multiline
          rows={4}
          placeholder={placeholder}
          value={content}
          onChange={(e) => setContent(e.target.value)}
          onKeyPress={handleKeyPress}
          variant="outlined"
          sx={{ mb: 2 }}
        />

        {selectedMood && (
          <Box mb={2}>
            <Chip
              label={`Feeling ${selectedMood}`}
              onDelete={() => setSelectedMood('')}
              color="primary"
              variant="outlined"
            />
          </Box>
        )}

        <Box display="flex" gap={1} mb={2}>
          <IconButton color="primary">
            <Image />
          </IconButton>
          <IconButton color="primary">
            <Mood />
          </IconButton>
          <IconButton color="primary">
            <LocationOn />
          </IconButton>
          <IconButton color="primary">
            <Poll />
          </IconButton>
        </Box>

        <Typography variant="body2" color="text.secondary">
          {content.length}/280 characters
        </Typography>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!content.trim() || content.length > 280}
        >
          Post
        </Button>
      </DialogActions>
    </Dialog>
  )
}

export default PostForm
