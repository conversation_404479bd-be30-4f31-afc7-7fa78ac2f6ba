import React, { useState, useEffect } from 'react'
import {
  Box,
  Typography,
  Container,
  Tabs,
  Tab,
  List,
  Button,
  CircularProgress,
  Chip,
  Paper,
  IconButton,
  Menu,
  MenuItem,
  Divider,
  useTheme
} from '@mui/material'
import {
  MarkEmailRead,
  FilterList,
  Delete,
  Settings,
  Notifications as NotificationsIcon
} from '@mui/icons-material'
import { useNotifications } from '../hooks'
import { NotificationItem } from '../components/notifications'

const NotificationsPage = () => {
  const theme = useTheme()
  const [tabValue, setTabValue] = useState(0)
  const [filterAnchorEl, setFilterAnchorEl] = useState(null)
  const [selectedType, setSelectedType] = useState('')

  const {
    notifications,
    unreadCount,
    loading,
    markAllAsRead,
    clearAll,
    getNotificationsByType,
    getUnreadNotifications
  } = useNotifications()

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue)
  }

  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget)
  }

  const handleFilterClose = () => {
    setFilterAnchorEl(null)
  }

  const handleTypeFilter = (type) => {
    setSelectedType(type)
    setTabValue(3) // Switch to filtered tab
    handleFilterClose()
  }

  const getFilteredNotifications = () => {
    switch (tabValue) {
      case 0: // All
        return selectedType ? getNotificationsByType(selectedType) : notifications
      case 1: // Unread
        return getUnreadNotifications()
      case 2: // Social
        return notifications.filter(n =>
          ['like_post', 'like_reel', 'comment_post', 'comment_reel', 'follow', 'mention'].includes(n.type)
        )
      case 3: // System
        return notifications.filter(n =>
          ['order_placed', 'payment_received', 'system_update', 'admin_announcement'].includes(n.type)
        )
      default:
        return notifications
    }
  }

  const filteredNotifications = getFilteredNotifications()

  const notificationTypes = [
    { value: '', label: 'All Types' },
    { value: 'like_post', label: 'Post Likes' },
    { value: 'comment_post', label: 'Post Comments' },
    { value: 'follow', label: 'New Followers' },
    { value: 'order_placed', label: 'Orders' },
    { value: 'message_received', label: 'Messages' }
  ]

  return (
    <Container maxWidth="md" sx={{ py: 3 }}>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
        <Box display="flex" alignItems="center" gap={2}>
          <Typography variant="h4" component="h1" fontWeight="600">
            Notifications
          </Typography>
          {unreadCount > 0 && (
            <Chip
              label={`${unreadCount} unread`}
              color="primary"
              size="small"
            />
          )}
        </Box>

        <Box display="flex" gap={1}>
          <IconButton
            onClick={handleFilterClick}
            title="Filter notifications"
          >
            <FilterList />
          </IconButton>

          {unreadCount > 0 && (
            <Button
              variant="outlined"
              size="small"
              startIcon={<MarkEmailRead />}
              onClick={markAllAsRead}
            >
              Mark All Read
            </Button>
          )}

          {notifications.length > 0 && (
            <Button
              variant="outlined"
              size="small"
              color="error"
              startIcon={<Delete />}
              onClick={clearAll}
            >
              Clear All
            </Button>
          )}
        </Box>
      </Box>

      {/* Filter Menu */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
      >
        {notificationTypes.map((type) => (
          <MenuItem
            key={type.value}
            onClick={() => handleTypeFilter(type.value)}
            selected={selectedType === type.value}
          >
            {type.label}
          </MenuItem>
        ))}
      </Menu>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 500
            }
          }}
        >
          <Tab
            label={`All (${notifications.length})`}
            icon={<NotificationsIcon />}
            iconPosition="start"
          />
          <Tab
            label={`Unread (${unreadCount})`}
            icon={<MarkEmailRead />}
            iconPosition="start"
          />
          <Tab label="Social" />
          <Tab label="System" />
        </Tabs>
      </Paper>

      {/* Content */}
      {loading ? (
        <Box display="flex" justifyContent="center" py={4}>
          <CircularProgress />
        </Box>
      ) : filteredNotifications.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <NotificationsIcon
            sx={{
              fontSize: 64,
              color: 'text.secondary',
              mb: 2
            }}
          />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            {tabValue === 1 ? 'No unread notifications' : 'No notifications'}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {tabValue === 1
              ? 'All caught up! Check back later for new notifications.'
              : 'When you receive notifications, they\'ll appear here.'
            }
          </Typography>
        </Paper>
      ) : (
        <Paper>
          <List sx={{ p: 0 }}>
            {filteredNotifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <NotificationItem notification={notification} />
                {index < filteredNotifications.length - 1 && (
                  <Divider variant="inset" />
                )}
              </React.Fragment>
            ))}
          </List>
        </Paper>
      )}
    </Container>
  )
}

export default NotificationsPage
